[2025-06-17 15:07:37,727] INFO - __main__ - main.py:562 - Logging configuration complete with immediate console output
[2025-06-17 15:07:37,730] INFO - __main__ - main.py:638 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-17 15:07:37,732] INFO - __main__ - main.py:817 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-17 15:07:37,738] INFO - __main__ - main.py:848 - Phase transition fixes imported successfully
[2025-06-17 15:07:37,744] WARNING - __main__ - main.py:3083 - Could not import from 'utils'. Using placeholder functions.
[2025-06-17 15:07:37,754] INFO - __main__ - main.py:3529 - FLASK: Using unified Firebase initialization approach...
[2025-06-17 15:07:37,757] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-17 15:07:37,840] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-17 15:07:37,840] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-17 15:07:38,348] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-17 15:07:38,350] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-17 15:07:39,144] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-17 15:07:40,059] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-17 15:07:40,059] INFO - __main__ - main.py:3537 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-17 15:07:40,060] INFO - __main__ - main.py:3627 - Gemini API will be initialized on first use (lazy loading).
[2025-06-17 15:07:40,101] INFO - __main__ - main.py:13716 - Starting Lesson Manager Service...
[2025-06-17 15:07:40,101] INFO - __main__ - main.py:13722 - Flask server starting on host 0.0.0.0, port 5000
[2025-06-17 15:07:40,102] INFO - __main__ - main.py:13723 - Debug mode: ON
[2025-06-17 15:07:40,220] INFO - werkzeug - _internal.py:97 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
[2025-06-17 15:07:40,221] INFO - werkzeug - _internal.py:97 - [33mPress CTRL+C to quit[0m
[2025-06-17 15:07:56,986] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-17 15:07:56,987] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "4e927171-662c-4d6e-844c-7ce9c96f1bd1", "timestamp": "2025-06-17T14:07:56.986744+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "subject": "Basic Science and Technology", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-17 15:07:56,988] INFO - __main__ - main.py:10228 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bk-5ulUmo_eIhHpT73P4BsfcaMvUV188KIuezkBqVFpDE0gsEisKSBHW5LGSQeei_ZqjofQAuhozlBjXkY8kcz5wNhIgbA6hF2BlquGxy1iRRAA35fVAa8M59xeQKOdypsFQNJABDE8UiBY8_Ny7UBIf-uzOWNebB3AEAmlsRDpPHUYLjYLsjoY_X4YHbnTxYZH6a0JcDk1VkJCGbdvCKvuLATCHs8ClSgeIWS4_ZnvOYf8TzjmqrRRAbAM39Gvhj4rI4Xzu9ezezpMQ-zgUeHFOMU3XSNgDJRDX8Je7YOM9CDY976uZkFoAaCRE77A4dH5USNA2M1SCG76qTClyVA', 'User-Agent': 'axios/1.9.0', 'Content-Length': '231', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-17 15:07:56,990] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Using temporary auth bypass with mock token
[2025-06-17 15:07:56,991] WARNING - __main__ - main.py:10235 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-17 15:07:56,992] INFO - __main__ - main.py:10242 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] lesson_content_and_quiz invoked by /lesson-content
[2025-06-17 15:07:56,993] INFO - __main__ - main.py:10258 - Lesson content request by authenticated user: andrea_ugono_33305 (Andrea Ugono)
[2025-06-17 15:07:56,993] INFO - __main__ - main.py:10262 - !!! [lesson_content_and_quiz] [4e927171-662c-4d6e-844c-7ce9c96f1bd1] RAW BODY AT START (bytes length: 231): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-BST-001","subject":"Basic Science and Technology","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-17 15:07:56,993] INFO - __main__ - main.py:1122 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] fetch_lesson_data: Fetching lesson with parameters:
[2025-06-17 15:07:56,994] INFO - __main__ - main.py:1123 -   • Country: Nigeria
[2025-06-17 15:07:56,994] INFO - __main__ - main.py:1124 -   • Curriculum: National Curriculum
[2025-06-17 15:07:56,994] INFO - __main__ - main.py:1125 -   • Grade: Primary 5
[2025-06-17 15:07:56,995] INFO - __main__ - main.py:1126 -   • Level: P5
[2025-06-17 15:07:56,996] INFO - __main__ - main.py:1127 -   • Subject: Basic Science and Technology
[2025-06-17 15:07:56,997] INFO - __main__ - main.py:1128 -   • Lesson ID: P5-BST-001
[2025-06-17 15:07:56,998] INFO - __main__ - main.py:1147 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Basic Science and Technology/lessonRef/P5-BST-001
[2025-06-17 15:07:57,333] INFO - __main__ - main.py:1211 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Successfully retrieved document with keys: ['additionalNotes', 'digitalMaterials', 'subject', 'metadata', 'instructionalSteps', 'id', 'existingAssessments', 'learningObjectives', 'lessonTimeLength', 'content', 'quizzes', 'gradeLevel', 'topic', 'lessonTitle', 'lessonRef', 'extensionActivities', 'country', 'theme', 'adaptiveStrategies', 'curriculumType', 'conclusion', 'quizzesAndAssessments', 'introduction']
[2025-06-17 15:07:57,334] INFO - __main__ - main.py:1362 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Extracted 10 key concepts: ['Identify', 'three', 'states', 'matter', 'Understand']...
[2025-06-17 15:07:57,334] INFO - __main__ - main.py:1442 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Universal content extraction: 346 characters from 3 steps
[2025-06-17 15:07:57,335] INFO - __main__ - main.py:1479 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Universal conversion: 3 steps → 3 sections
[2025-06-17 15:07:57,336] INFO - __main__ - main.py:1297 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Field mapping completed:
[2025-06-17 15:07:57,336] INFO - __main__ - main.py:1298 -   - Subject: Basic Science and Technology
[2025-06-17 15:07:57,337] INFO - __main__ - main.py:1299 -   - Topic: States of Matter
[2025-06-17 15:07:57,338] INFO - __main__ - main.py:1300 -   - Grade: Primary 5
[2025-06-17 15:07:57,338] INFO - __main__ - main.py:1301 -   - Key Concepts: 10 extracted
[2025-06-17 15:07:57,339] INFO - __main__ - main.py:1302 -   - Instructional Steps: 3
[2025-06-17 15:07:57,340] INFO - __main__ - main.py:1500 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] ✅ Universal content structure recognized: instructionalSteps (3 steps)
[2025-06-17 15:07:57,341] INFO - __main__ - main.py:1515 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] ✅ All required fields present after universal mapping
[2025-06-17 15:07:57,341] INFO - __main__ - main.py:1220 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Successfully mapped lesson fields for AI inference
[2025-06-17 15:07:57,341] DEBUG - __main__ - main.py:602 - Cached result for fetch_lesson_data
[2025-06-17 15:07:57,342] INFO - __main__ - main.py:10355 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] ✅ USING existing 2 learning objectives
[2025-06-17 15:07:57,343] INFO - __main__ - main.py:10356 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] 🎯 EXISTING OBJECTIVES: ['Identify the three states of matter.', 'Understand how matter changes state.']
[2025-06-17 15:07:57,897] INFO - __main__ - main.py:10362 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] 💾 SAVED learning objectives to Firestore
[2025-06-17 15:07:57,897] INFO - __main__ - main.py:8778 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Attempting to initialize lesson session for student_id: andrea_ugono_33305
[2025-06-17 15:07:57,898] DEBUG - __main__ - main.py:8779 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'digitalMaterials', 'metadata', 'id', 'existingAssessments', 'gradeLevel', 'extensionActivities', 'country', 'theme', 'curriculumType', 'quizzesAndAssessments', 'curriculum', 'level']
[2025-06-17 15:07:57,898] INFO - __main__ - main.py:8818 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Using student name: 'Andrea Ugono' for session session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:07:57,899] INFO - __main__ - main.py:1537 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Constructing initial lesson state data for Firestore session: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:07:57,900] INFO - __main__ - main.py:1587 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] ✅ Constructed initial state dictionary for session session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80. Phase: diagnostic_start_probe
[2025-06-17 15:07:57,901] INFO - __main__ - main.py:1588 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-17 15:07:57,902] INFO - __main__ - main.py:8871 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Preparing to create 'lesson_sessions' doc 'session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80'.
[2025-06-17 15:07:57,903] WARNING - __main__ - main.py:963 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-17 15:07:57,904] DEBUG - __main__ - main.py:8872 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80', 'student_id': 'andrea_ugono_33305', 'lessonRef': 'P5-BST-001', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Basic Science and Technology', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Andrea Ugono', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'States of Matter', 'topic': 'States of Matter', 'learningObjectives': ['Identify the three states of matter.', 'Understand how matter changes state.'], 'key_concepts': ['Identify', 'three', 'states', 'matter', 'Understand', 'changes', 'state', 'Warm', 'Up Activity', 'Show'], 'metadata': {'blooms_level': ['Remembering', 'Understanding'], 'context': 'Introduction', 'apiConnections': ["Gemini: This lesson does not rely on Gemini for Bloom's Taxonomy analysis."], 'skills': ['Understanding', 'Applying'], 'difficulty': 'easy', 'taxonomy_alignment': 'This lesson focuses on introducing the three states of matter and how matter changes between these states.'}}
[2025-06-17 15:07:57,904] INFO - __main__ - main.py:8874 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Preparing to create 'lesson_states' doc 'session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80'.
[2025-06-17 15:07:57,905] WARNING - __main__ - main.py:963 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-17 15:07:57,905] DEBUG - __main__ - main.py:8875 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] 'lesson_states' data: {'session_id': 'session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80', 'student_id': 'andrea_ugono_33305', 'student_name': 'Andrea Ugono', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-BST-001', 'subject': 'Basic Science and Technology', 'grade': 'Primary 5', 'topic': 'States of Matter', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Identify, three, states, matter, Understand, changes, state, Warm, Up Activity, Show', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-17 15:07:58,284] INFO - __main__ - main.py:8885 - [4e927171-662c-4d6e-844c-7ce9c96f1bd1] Successfully created Firestore docs for session 'session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80', student 'andrea_ugono_33305'
[2025-06-17 15:07:58,287] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:07:58] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-17 15:07:58,557] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:07:58,558] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "e82b494b-778f-4b2e-8a78-000180dda299", "timestamp": "2025-06-17T14:07:58.558349+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": []}}
[2025-06-17 15:07:58,560] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [e82b494b-778f-4b2e-8a78-000180dda299] Using temporary auth bypass with mock token
[2025-06-17 15:07:58,562] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:07:58,569] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:07:58,571] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:07:58,571] INFO - __main__ - main.py:5180 - [enhance_content_api][e82b494b-778f-4b2e-8a78-000180dda299] Processing enhance content request
[2025-06-17 15:07:58,572] INFO - __main__ - main.py:5226 - [e82b494b-778f-4b2e-8a78-000180dda299][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': 'Start diagnostic assessment', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Basic Science and Techno
[2025-06-17 15:07:58,885] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:07:58,885] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:07:58,885] INFO - __main__ - main.py:5272 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:07:58,886] INFO - __main__ - main.py:5311 - [e82b494b-778f-4b2e-8a78-000180dda299] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:07:58,886] INFO - __main__ - main.py:2273 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:07:58,888] INFO - __main__ - main.py:3618 - Gemini API configured successfully with gemini-1.5-flash and safety filters disabled.
[2025-06-17 15:07:59,447] INFO - __main__ - main.py:2332 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:07:59,448] INFO - __main__ - main.py:2332 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:07:59,448] INFO - __main__ - main.py:2332 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:07:59,448] INFO - __main__ - main.py:2332 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:07:59,449] INFO - __main__ - main.py:2332 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:07:59,449] INFO - __main__ - main.py:2401 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:07:59,450] DEBUG - __main__ - main.py:2415 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:07:59,450] DEBUG - __main__ - main.py:2418 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:07:59,450] DEBUG - __main__ - main.py:2419 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:07:59,451] DEBUG - __main__ - main.py:2420 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:07:59,451] INFO - __main__ - main.py:2424 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:08:00,825] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:08:00,826] INFO - __main__ - main.py:2434 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Gemini API call completed in 1.37s. Raw response: 'materials'
[2025-06-17 15:08:00,827] DEBUG - __main__ - main.py:2456 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:08:00,828] INFO - __main__ - main.py:2461 - [e82b494b-778f-4b2e-8a78-000180dda299] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:08:00,829] INFO - __main__ - main.py:5344 - [e82b494b-778f-4b2e-8a78-000180dda299] Successfully inferred module ID via AI: materials
[2025-06-17 15:08:00,831] INFO - __main__ - main.py:2510 - [e82b494b-778f-4b2e-8a78-000180dda299] CACHE MISS or fetch: Getting GS levels for subject 'science', module 'materials'.
[2025-06-17 15:08:01,398] INFO - __main__ - main.py:2533 - [e82b494b-778f-4b2e-8a78-000180dda299] Fetched metadata for module: 'Materials'
[2025-06-17 15:08:01,857] INFO - __main__ - main.py:2565 - [e82b494b-778f-4b2e-8a78-000180dda299] Successfully fetched 10 levels for module 'materials'.
[2025-06-17 15:08:01,858] INFO - __main__ - main.py:5370 - [e82b494b-778f-4b2e-8a78-000180dda299] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:08:02,384] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:08:02,717] WARNING - __main__ - main.py:5391 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:08:02,717] WARNING - __main__ - main.py:5392 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Session exists: True
[2025-06-17 15:08:02,718] WARNING - __main__ - main.py:5393 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Current phase: diagnostic_start_probe
[2025-06-17 15:08:02,719] WARNING - __main__ - main.py:5394 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'current_phase', 'diagnostic_completed_this_session', 'student_name', 'lesson_context_snapshot', 'current_probing_level_number', 'levels_probed_and_failed', 'assigned_level_for_teaching', 'current_lesson_phase', 'current_question_index', 'student_id', 'last_modified', 'student_answers_for_probing_level']
[2025-06-17 15:08:02,722] INFO - __main__ - main.py:5458 - [e82b494b-778f-4b2e-8a78-000180dda299] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:08:02,723] INFO - __main__ - main.py:5459 - [e82b494b-778f-4b2e-8a78-000180dda299]   assigned_level_for_teaching (session): None
[2025-06-17 15:08:02,723] INFO - __main__ - main.py:5460 - [e82b494b-778f-4b2e-8a78-000180dda299]   latest_assessed_level (profile): None
[2025-06-17 15:08:02,723] INFO - __main__ - main.py:5461 - [e82b494b-778f-4b2e-8a78-000180dda299]   teaching_level_for_returning_student: None
[2025-06-17 15:08:02,724] INFO - __main__ - main.py:5462 - [e82b494b-778f-4b2e-8a78-000180dda299]   has_completed_diagnostic_before: False
[2025-06-17 15:08:02,724] INFO - __main__ - main.py:5463 - [e82b494b-778f-4b2e-8a78-000180dda299]   is_first_encounter_for_module: True
[2025-06-17 15:08:02,725] WARNING - __main__ - main.py:5468 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:08:02,725] INFO - __main__ - main.py:5474 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:08:02,726] INFO - __main__ - main.py:5475 - [e82b494b-778f-4b2e-8a78-000180dda299]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-17 15:08:02,726] INFO - __main__ - main.py:5476 - [e82b494b-778f-4b2e-8a78-000180dda299]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:08:02,726] INFO - __main__ - main.py:5477 - [e82b494b-778f-4b2e-8a78-000180dda299]   Is first encounter: True
[2025-06-17 15:08:02,727] INFO - __main__ - main.py:5478 - [e82b494b-778f-4b2e-8a78-000180dda299]   Diagnostic completed: False
[2025-06-17 15:08:02,727] INFO - __main__ - main.py:5484 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-17 15:08:02,727] INFO - __main__ - main.py:5498 - [e82b494b-778f-4b2e-8a78-000180dda299] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:08:02,728] INFO - __main__ - main.py:5500 - [e82b494b-778f-4b2e-8a78-000180dda299] Final phase for AI logic: diagnostic_start_probe
[2025-06-17 15:08:02,728] INFO - __main__ - main.py:5520 - [e82b494b-778f-4b2e-8a78-000180dda299] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-17 15:08:02,729] INFO - __main__ - main.py:3692 - [e82b494b-778f-4b2e-8a78-000180dda299] Diagnostic context validation passed
[2025-06-17 15:08:02,730] INFO - __main__ - main.py:3719 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-17 15:08:02,730] WARNING - __main__ - main.py:5586 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-17 15:08:02,731] INFO - __main__ - main.py:3802 - [e82b494b-778f-4b2e-8a78-000180dda299] Enhanced diagnostic context with 35 fields
[2025-06-17 15:08:02,732] INFO - __main__ - main.py:5608 - [e82b494b-778f-4b2e-8a78-000180dda299] Robust diagnostic context prepared successfully. Phase: diagnostic_start_probe
[2025-06-17 15:08:02,732] DEBUG - __main__ - main.py:5609 - [e82b494b-778f-4b2e-8a78-000180dda299] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:08:02,733] WARNING - __main__ - main.py:5617 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:08:02,733] WARNING - __main__ - main.py:5618 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:08:02,733] WARNING - __main__ - main.py:5619 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖   - Student query: Start diagnostic assessment...
[2025-06-17 15:08:02,734] WARNING - __main__ - main.py:5620 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:08:02,735] INFO - __main__ - main.py:6493 - [e82b494b-778f-4b2e-8a78-000180dda299] enhance_lesson_content invoked. Query: 'Start diagnostic assessment...'
[2025-06-17 15:08:02,735] INFO - __main__ - main.py:6536 - [e82b494b-778f-4b2e-8a78-000180dda299] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_start_probe', processed = 'diagnostic_start_probe'
[2025-06-17 15:08:02,736] INFO - __main__ - main.py:6547 - [e82b494b-778f-4b2e-8a78-000180dda299][enhance_lesson_content] Received from context - phase: 'diagnostic_start_probe', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:08:02,736] INFO - __main__ - main.py:6581 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC ANSWER: Storing sequential answer 1
[2025-06-17 15:08:02,736] INFO - __main__ - main.py:6586 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC ANSWER STORED: q1 = 'Start diagnostic assessment...'
[2025-06-17 15:08:02,737] INFO - __main__ - main.py:6587 - [e82b494b-778f-4b2e-8a78-000180dda299] Total diagnostic answers now: 1/5
[2025-06-17 15:08:02,737] INFO - __main__ - main.py:6628 - [e82b494b-778f-4b2e-8a78-000180dda299] 📝 DIAGNOSTIC PROGRESSION: Continue with 1/5 answers, phase: diagnostic_start_probe
[2025-06-17 15:08:02,737] INFO - __main__ - main.py:6766 - [e82b494b-778f-4b2e-8a78-000180dda299][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_start_probe
[2025-06-17 15:08:02,738] INFO - __main__ - main.py:6773 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] Phase: diagnostic_start_probe
[2025-06-17 15:08:02,738] INFO - __main__ - main.py:6774 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] Questions asked: 1/5
[2025-06-17 15:08:02,738] INFO - __main__ - main.py:6775 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 15:08:02,738] INFO - __main__ - main.py:6776 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] Student answers count: 1
[2025-06-17 15:08:02,739] INFO - __main__ - main.py:6781 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] Answer q1: Start diagnostic assessment...
[2025-06-17 15:08:02,739] INFO - __main__ - main.py:6805 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_start_probe, Answers=1/5
[2025-06-17 15:08:02,739] INFO - __main__ - main.py:6808 - [e82b494b-778f-4b2e-8a78-000180dda299][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:08:02,739] INFO - __main__ - main.py:6839 - [e82b494b-778f-4b2e-8a78-000180dda299] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:08:02,739] INFO - __main__ - main.py:6853 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_start_probe
[2025-06-17 15:08:02,740] INFO - __main__ - main.py:6861 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_start_probe'
[2025-06-17 15:08:02,740] INFO - __main__ - main.py:6870 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC PROGRESSION: First/system interaction - staying in diagnostic_start_probe for introduction
[2025-06-17 15:08:02,740] INFO - __main__ - main.py:7140 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯 ENHANCED PHASE CALCULATION: diagnostic_start_probe → diagnostic_start_probe (interaction 1)
[2025-06-17 15:08:02,741] WARNING - __main__ - main.py:7143 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:08:02,741] WARNING - __main__ - main.py:7144 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Input phase from context: 'diagnostic_start_probe'
[2025-06-17 15:08:02,741] WARNING - __main__ - main.py:7145 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - User query: 'Start diagnostic assessment'
[2025-06-17 15:08:02,742] WARNING - __main__ - main.py:7146 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Is actual student response: True
[2025-06-17 15:08:02,742] WARNING - __main__ - main.py:7147 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Current probing level: 5
[2025-06-17 15:08:02,742] WARNING - __main__ - main.py:7148 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Current question index: 0
[2025-06-17 15:08:02,742] WARNING - __main__ - main.py:7149 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Student answers count: 1
[2025-06-17 15:08:02,743] INFO - __main__ - main.py:7152 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:08:02,743] INFO - __main__ - main.py:7153 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧   - lesson_phase_from_context: 'diagnostic_start_probe'
[2025-06-17 15:08:02,743] INFO - __main__ - main.py:7154 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_start_probe'
[2025-06-17 15:08:02,744] INFO - __main__ - main.py:7155 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧   - user_query: 'Start diagnostic assessment'
[2025-06-17 15:08:02,744] INFO - __main__ - main.py:7156 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:08:02,744] WARNING - __main__ - main.py:7159 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:08:02,745] WARNING - __main__ - main.py:7160 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯   - Current phase: 'diagnostic_start_probe'
[2025-06-17 15:08:02,745] WARNING - __main__ - main.py:7161 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯   - Calculated new phase: 'diagnostic_start_probe'
[2025-06-17 15:08:02,745] WARNING - __main__ - main.py:7162 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯   - Phase changed: False
[2025-06-17 15:08:02,746] WARNING - __main__ - main.py:7165 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:08:02,747] WARNING - __main__ - main.py:7166 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯   - In diagnostic phase: True
[2025-06-17 15:08:02,747] WARNING - __main__ - main.py:7167 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯   - Student answers stored: {'q1': 'Start diagnostic assessment'}
[2025-06-17 15:08:02,748] WARNING - __main__ - main.py:7168 - [e82b494b-778f-4b2e-8a78-000180dda299] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:08:02,749] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:08:02,749] INFO - __main__ - main.py:7229 - [e82b494b-778f-4b2e-8a78-000180dda299][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_start_probe', q_index='0', total_q_asked='1'
[2025-06-17 15:08:02,750] INFO - __main__ - main.py:7235 - [e82b494b-778f-4b2e-8a78-000180dda299][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:08:02,751] INFO - __main__ - main.py:7340 - [e82b494b-778f-4b2e-8a78-000180dda299] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:08:02,751] INFO - __main__ - main.py:7351 - [e82b494b-778f-4b2e-8a78-000180dda299] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:08:02,752] INFO - __main__ - main.py:7357 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Template placeholder successfully substituted
[2025-06-17 15:08:02,752] INFO - __main__ - main.py:7361 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Template contains calculated phase: diagnostic_start_probe
[2025-06-17 15:08:02,753] INFO - __main__ - main.py:7405 - [e82b494b-778f-4b2e-8a78-000180dda299] Prompt truncated from 11936 to 10572 chars for performance
[2025-06-17 15:08:02,753] INFO - __main__ - main.py:7408 - [e82b494b-778f-4b2e-8a78-000180dda299] CONTENT QUALITY DEBUG:
[2025-06-17 15:08:02,753] INFO - __main__ - main.py:7409 -   - Final prompt length: 10572 characters
[2025-06-17 15:08:02,753] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:08:02,754] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:08:02,754] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:08:02,754] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:08:02,755] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_start_probe
[2025-06-17 15:08:02,755] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:08:02,755] DEBUG - __main__ - main.py:7416 - [e82b494b-778f-4b2e-8a78-000180dda299] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_start_probe", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_start_probe
• You MUST use this exact phase name in your state upd...
[2025-06-17 15:08:02,756] INFO - __main__ - main.py:7444 - [e82b494b-778f-4b2e-8a78-000180dda299] Gemini API call attempt 1/3
[2025-06-17 15:08:04,002] INFO - __main__ - main.py:7462 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:08:04,002] INFO - __main__ - main.py:7481 - [e82b494b-778f-4b2e-8a78-000180dda299] Gemini API call completed in 1.25s
[2025-06-17 15:08:04,003] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 485 characters
[2025-06-17 15:08:04,003] INFO - __main__ - main.py:7704 - [e82b494b-778f-4b2e-8a78-000180dda299][enhance_lesson_content] AI response: Hi Andrea! I'm your Basic Science and Technology tutor, and I'm excited to help you learn about States of Matter.  We'll start with a short diagnostic assessment to understand your current knowledge. ...
[2025-06-17 15:08:04,004] INFO - __main__ - main.py:7732 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:08:04,005] INFO - __main__ - main.py:7733 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 STATE DEBUG: AI response length: 485 chars
[2025-06-17 15:08:04,007] INFO - __main__ - main.py:7745 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_start_probe", "interaction_count": 1}
[2025-06-17 15:08:04,008] INFO - __main__ - main.py:7747 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Found AI state update: {'new_phase': 'diagnostic_start_probe', 'interaction_count': 1}
[2025-06-17 15:08:04,009] INFO - __main__ - main.py:7811 - [e82b494b-778f-4b2e-8a78-000180dda299] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:08:04,010] INFO - __main__ - main.py:7817 - [e82b494b-778f-4b2e-8a78-000180dda299] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:08:04,010] INFO - __main__ - main.py:7837 - [e82b494b-778f-4b2e-8a78-000180dda299] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:08:04,011] INFO - __main__ - main.py:7841 - [e82b494b-778f-4b2e-8a78-000180dda299] Auto-corrected: added missing current_question_index
[2025-06-17 15:08:04,012] INFO - __main__ - main.py:7867 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ AI state validation completed: {'new_phase': 'diagnostic_start_probe', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 15:08:04,012] INFO - __main__ - main.py:7879 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:08:04,014] INFO - __main__ - main.py:7922 - [e82b494b-778f-4b2e-8a78-000180dda299] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:08:04,015] INFO - __main__ - main.py:7931 - [e82b494b-778f-4b2e-8a78-000180dda299] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_start_probe', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 15:08:04,016] INFO - __main__ - main.py:7989 - [e82b494b-778f-4b2e-8a78-000180dda299] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=1/5
[2025-06-17 15:08:04,017] INFO - __main__ - main.py:7992 - [e82b494b-778f-4b2e-8a78-000180dda299] 📝 DIAGNOSTIC PROGRESSION: Need 4 more answers
[2025-06-17 15:08:04,017] INFO - __main__ - main.py:7994 - [e82b494b-778f-4b2e-8a78-000180dda299] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:08:04,018] INFO - __main__ - main.py:7998 - [e82b494b-778f-4b2e-8a78-000180dda299] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:08:04,018] INFO - __main__ - main.py:8026 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC PROGRESSION: AI stuck in same phase - applying calculated fallback: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 15:08:04,018] INFO - __main__ - main.py:8075 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 15:08:04,019] WARNING - __main__ - main.py:8113 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 15:08:04,019] WARNING - __main__ - main.py:8114 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Phase: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 15:08:04,019] WARNING - __main__ - main.py:8115 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Question Index: 0
[2025-06-17 15:08:04,020] WARNING - __main__ - main.py:8116 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   - Probing Level: 5
[2025-06-17 15:08:04,020] INFO - __main__ - main.py:8120 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 15:08:04,021] INFO - __main__ - main.py:8121 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_start_probe, Enforced phase: diagnostic_start_probe
[2025-06-17 15:08:04,021] INFO - __main__ - main.py:8141 - [e82b494b-778f-4b2e-8a78-000180dda299] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:08:04,021] DEBUG - __main__ - main.py:8207 - [e82b494b-778f-4b2e-8a78-000180dda299] Phase unchanged: diagnostic_start_probe
[2025-06-17 15:08:04,022] INFO - __main__ - main.py:8223 - [e82b494b-778f-4b2e-8a78-000180dda299] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:04,023] INFO - __main__ - main.py:8259 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:08:04,023] INFO - __main__ - main.py:8275 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ AI response already includes personalization
[2025-06-17 15:08:04,023] INFO - __main__ - main.py:8281 - [e82b494b-778f-4b2e-8a78-000180dda299] [OK] State updates processed: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:04,024] INFO - __main__ - main.py:8284 - [e82b494b-778f-4b2e-8a78-000180dda299] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:08:04,024] WARNING - __main__ - main.py:8305 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:08:04,024] WARNING - __main__ - main.py:8306 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔄   - AI provided state update: True
[2025-06-17 15:08:04,024] WARNING - __main__ - main.py:8308 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔄   - AI proposed phase: 'diagnostic_start_probe'
[2025-06-17 15:08:04,025] WARNING - __main__ - main.py:8309 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔄   - Current lesson phase: 'diagnostic_start_probe'
[2025-06-17 15:08:04,025] WARNING - __main__ - main.py:8310 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔄   - Python calculated phase: 'diagnostic_start_probe'
[2025-06-17 15:08:04,025] INFO - __main__ - main.py:8318 - [e82b494b-778f-4b2e-8a78-000180dda299] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:08:04,026] INFO - __main__ - main.py:8319 -   - Total execution time: 1.291s
[2025-06-17 15:08:04,026] WARNING - __main__ - main.py:5643 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:08:04,026] WARNING - __main__ - main.py:5644 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖   - Content length: 359 chars
[2025-06-17 15:08:04,027] WARNING - __main__ - main.py:5645 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖   - State updates: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:04,027] WARNING - __main__ - main.py:5646 - [e82b494b-778f-4b2e-8a78-000180dda299] 🤖   - Raw state block: None...
[2025-06-17 15:08:04,027] INFO - __main__ - main.py:5662 - [e82b494b-778f-4b2e-8a78-000180dda299] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:08:04,028] INFO - __main__ - main.py:5663 - [e82b494b-778f-4b2e-8a78-000180dda299] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-17 15:08:04,332] INFO - __main__ - main.py:5772 - [e82b494b-778f-4b2e-8a78-000180dda299] AI processing completed in 1.60s
[2025-06-17 15:08:04,332] WARNING - __main__ - main.py:5783 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-17 15:08:04,333] INFO - __main__ - main.py:3897 - [e82b494b-778f-4b2e-8a78-000180dda299] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 15:08:04,334] WARNING - __main__ - main.py:5792 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:08:04,335] WARNING - __main__ - main.py:5799 - [e82b494b-778f-4b2e-8a78-000180dda299] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-17 15:08:04,335] WARNING - __main__ - main.py:5806 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:08:04,336] WARNING - __main__ - main.py:5807 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-17 15:08:04,337] WARNING - __main__ - main.py:5808 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:08:04,338] WARNING - __main__ - main.py:5809 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   3. AI state updates: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:04,339] WARNING - __main__ - main.py:5810 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-17 15:08:04,339] WARNING - __main__ - main.py:5813 - [e82b494b-778f-4b2e-8a78-000180dda299] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:08:04,340] WARNING - __main__ - main.py:5814 - [e82b494b-778f-4b2e-8a78-000180dda299] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-17 15:08:04,340] WARNING - __main__ - main.py:5815 - [e82b494b-778f-4b2e-8a78-000180dda299] 💾   - State updates from AI: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:04,341] WARNING - __main__ - main.py:5816 - [e82b494b-778f-4b2e-8a78-000180dda299] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-17 15:08:04,341] WARNING - __main__ - main.py:5817 - [e82b494b-778f-4b2e-8a78-000180dda299] 💾   - Phase change: False
[2025-06-17 15:08:04,342] INFO - __main__ - main.py:3929 - [e82b494b-778f-4b2e-8a78-000180dda299] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:08:04,342] INFO - __main__ - main.py:3930 - [e82b494b-778f-4b2e-8a78-000180dda299]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-17 15:08:04,343] INFO - __main__ - main.py:3931 - [e82b494b-778f-4b2e-8a78-000180dda299]   Current level: 5
[2025-06-17 15:08:04,343] INFO - __main__ - main.py:3932 - [e82b494b-778f-4b2e-8a78-000180dda299]   Question index: 0
[2025-06-17 15:08:04,344] INFO - __main__ - main.py:3933 - [e82b494b-778f-4b2e-8a78-000180dda299]   First encounter: True
[2025-06-17 15:08:04,344] INFO - __main__ - main.py:3938 - [e82b494b-778f-4b2e-8a78-000180dda299]   Answers collected: 1
[2025-06-17 15:08:04,345] INFO - __main__ - main.py:3939 - [e82b494b-778f-4b2e-8a78-000180dda299]   Levels failed: 0
[2025-06-17 15:08:04,346] INFO - __main__ - main.py:3897 - [e82b494b-778f-4b2e-8a78-000180dda299] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 15:08:04,346] INFO - __main__ - main.py:3943 - [e82b494b-778f-4b2e-8a78-000180dda299]   State update valid: True
[2025-06-17 15:08:04,347] INFO - __main__ - main.py:3950 - [e82b494b-778f-4b2e-8a78-000180dda299]   Diagnostic complete: False
[2025-06-17 15:08:04,348] WARNING - __main__ - main.py:5829 - [e82b494b-778f-4b2e-8a78-000180dda299] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:08:04,911] WARNING - __main__ - main.py:5856 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:08:04,912] WARNING - __main__ - main.py:5857 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Phase: diagnostic_start_probe
[2025-06-17 15:08:04,913] WARNING - __main__ - main.py:5858 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Probing Level: 5
[2025-06-17 15:08:04,913] WARNING - __main__ - main.py:5859 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Question Index: 0
[2025-06-17 15:08:04,914] WARNING - __main__ - main.py:5860 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Diagnostic Complete: False
[2025-06-17 15:08:05,786] INFO - __main__ - main.py:5919 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:08:05,787] WARNING - __main__ - main.py:5920 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:08:05,787] WARNING - __main__ - main.py:5921 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:08:05,788] WARNING - __main__ - main.py:5922 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 15:08:05,788] WARNING - __main__ - main.py:5923 - [e82b494b-778f-4b2e-8a78-000180dda299] ✅   - Interaction logged successfully
[2025-06-17 15:08:05,790] INFO - __main__ - main.py:9216 - [e82b494b-778f-4b2e-8a78-000180dda299] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:08:05,791] DEBUG - __main__ - main.py:2753 - [e82b494b-778f-4b2e-8a78-000180dda299] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:08:05,792] DEBUG - __main__ - main.py:5969 - [e82b494b-778f-4b2e-8a78-000180dda299] No final assessment data found in AI response
[2025-06-17 15:08:05,794] INFO - __main__ - main.py:6034 - [e82b494b-778f-4b2e-8a78-000180dda299] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:08:05,796] WARNING - __main__ - main.py:625 - High response time detected: 7.24s for enhance_content_api
[2025-06-17 15:08:05,798] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:08:05] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 15:08:14,279] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:08:14,281] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "2772f8b4-d752-4bc5-a615-cf4a1b335491", "timestamp": "2025-06-17T14:08:14.280625+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "Yes I'm ready", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your Basic Science and Technology tutor, and I'm excited to help you learn about States of Matter. We'll start with a short diagnostic assessment to understand your current knowledge. This helps me tailor the best learning experience for you. Are you ready to begin with some questions about Discover, Three, States, Matter, and Transformation?", "timestamp": "2025-06-17T14:08:05.816Z"}]}}
[2025-06-17 15:08:14,283] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Using temporary auth bypass with mock token
[2025-06-17 15:08:14,284] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:08:14,289] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:08:14,291] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:08:14,291] INFO - __main__ - main.py:5180 - [enhance_content_api][2772f8b4-d752-4bc5-a615-cf4a1b335491] Processing enhance content request
[2025-06-17 15:08:14,292] INFO - __main__ - main.py:5226 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': "Yes I'm ready", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Basic Science and Technology', 'sessio
[2025-06-17 15:08:14,587] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:08:14,587] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:08:14,588] INFO - __main__ - main.py:5272 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:08:14,588] INFO - __main__ - main.py:5311 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:08:14,588] INFO - __main__ - main.py:2273 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:08:15,320] INFO - __main__ - main.py:2332 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:08:15,320] INFO - __main__ - main.py:2332 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:08:15,320] INFO - __main__ - main.py:2332 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:08:15,321] INFO - __main__ - main.py:2332 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:08:15,321] INFO - __main__ - main.py:2332 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:08:15,322] INFO - __main__ - main.py:2401 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:08:15,322] DEBUG - __main__ - main.py:2415 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:08:15,323] DEBUG - __main__ - main.py:2418 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:08:15,324] DEBUG - __main__ - main.py:2419 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:08:15,324] DEBUG - __main__ - main.py:2420 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:08:15,325] INFO - __main__ - main.py:2424 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:08:16,059] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:08:16,059] INFO - __main__ - main.py:2434 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Gemini API call completed in 0.73s. Raw response: 'materials'
[2025-06-17 15:08:16,060] DEBUG - __main__ - main.py:2456 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:08:16,060] INFO - __main__ - main.py:2461 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:08:16,061] INFO - __main__ - main.py:5344 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Successfully inferred module ID via AI: materials
[2025-06-17 15:08:16,062] INFO - __main__ - main.py:5370 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:08:16,349] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:08:17,032] WARNING - __main__ - main.py:5391 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:08:17,032] WARNING - __main__ - main.py:5392 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Session exists: True
[2025-06-17 15:08:17,033] WARNING - __main__ - main.py:5393 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Current phase: diagnostic_start_probe
[2025-06-17 15:08:17,034] WARNING - __main__ - main.py:5394 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'diagnostic_completed_this_session', 'current_probing_level_number', 'lesson_context_snapshot', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'current_phase', 'is_first_encounter_for_module', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 15:08:17,035] INFO - __main__ - main.py:5458 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:08:17,036] INFO - __main__ - main.py:5459 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   assigned_level_for_teaching (session): None
[2025-06-17 15:08:17,037] INFO - __main__ - main.py:5460 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   latest_assessed_level (profile): None
[2025-06-17 15:08:17,038] INFO - __main__ - main.py:5461 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   teaching_level_for_returning_student: None
[2025-06-17 15:08:17,038] INFO - __main__ - main.py:5462 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   has_completed_diagnostic_before: False
[2025-06-17 15:08:17,038] INFO - __main__ - main.py:5463 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   is_first_encounter_for_module: True
[2025-06-17 15:08:17,039] WARNING - __main__ - main.py:5468 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:08:17,039] INFO - __main__ - main.py:5474 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:08:17,040] INFO - __main__ - main.py:5475 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-17 15:08:17,040] INFO - __main__ - main.py:5476 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:08:17,040] INFO - __main__ - main.py:5477 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Is first encounter: True
[2025-06-17 15:08:17,041] INFO - __main__ - main.py:5478 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Diagnostic completed: False
[2025-06-17 15:08:17,041] INFO - __main__ - main.py:5484 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-17 15:08:17,042] INFO - __main__ - main.py:5498 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:08:17,042] INFO - __main__ - main.py:5500 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Final phase for AI logic: diagnostic_start_probe
[2025-06-17 15:08:17,042] INFO - __main__ - main.py:5520 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-17 15:08:17,043] INFO - __main__ - main.py:3692 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Diagnostic context validation passed
[2025-06-17 15:08:17,043] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_start_probe'
[2025-06-17 15:08:17,043] WARNING - __main__ - main.py:5586 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-17 15:08:17,044] INFO - __main__ - main.py:3802 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Enhanced diagnostic context with 35 fields
[2025-06-17 15:08:17,044] INFO - __main__ - main.py:5608 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Robust diagnostic context prepared successfully. Phase: diagnostic_start_probe
[2025-06-17 15:08:17,045] DEBUG - __main__ - main.py:5609 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:08:17,046] WARNING - __main__ - main.py:5617 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:08:17,047] WARNING - __main__ - main.py:5618 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:08:17,048] WARNING - __main__ - main.py:5619 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖   - Student query: Yes I'm ready...
[2025-06-17 15:08:17,049] WARNING - __main__ - main.py:5620 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:08:17,050] INFO - __main__ - main.py:6493 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] enhance_lesson_content invoked. Query: 'Yes I'm ready...'
[2025-06-17 15:08:17,050] INFO - __main__ - main.py:6536 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_start_probe', processed = 'diagnostic_start_probe'
[2025-06-17 15:08:17,050] INFO - __main__ - main.py:6547 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][enhance_lesson_content] Received from context - phase: 'diagnostic_start_probe', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:08:17,051] INFO - __main__ - main.py:6581 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC ANSWER: Storing sequential answer 2
[2025-06-17 15:08:17,051] INFO - __main__ - main.py:6586 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC ANSWER STORED: q2 = 'Yes I'm ready...'
[2025-06-17 15:08:17,051] INFO - __main__ - main.py:6587 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Total diagnostic answers now: 2/5
[2025-06-17 15:08:17,052] INFO - __main__ - main.py:6628 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 📝 DIAGNOSTIC PROGRESSION: Continue with 2/5 answers, phase: diagnostic_start_probe
[2025-06-17 15:08:17,052] INFO - __main__ - main.py:6766 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_start_probe
[2025-06-17 15:08:17,052] INFO - __main__ - main.py:6773 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] Phase: diagnostic_start_probe
[2025-06-17 15:08:17,053] INFO - __main__ - main.py:6774 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] Questions asked: 2/5
[2025-06-17 15:08:17,053] INFO - __main__ - main.py:6775 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 15:08:17,053] INFO - __main__ - main.py:6776 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] Student answers count: 2
[2025-06-17 15:08:17,054] INFO - __main__ - main.py:6781 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] Answer q1: Start diagnostic assessment...
[2025-06-17 15:08:17,054] INFO - __main__ - main.py:6781 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 15:08:17,055] INFO - __main__ - main.py:6805 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_start_probe, Answers=2/5
[2025-06-17 15:08:17,055] INFO - __main__ - main.py:6808 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:08:17,055] INFO - __main__ - main.py:6839 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:08:17,055] INFO - __main__ - main.py:6853 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_start_probe
[2025-06-17 15:08:17,056] INFO - __main__ - main.py:6861 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_start_probe'
[2025-06-17 15:08:17,056] INFO - __main__ - main.py:6881 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC PROGRESSION: Student responded 'yes i'm ready' - AI should transition to diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:17,056] INFO - __main__ - main.py:6887 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] NATURAL PROGRESSION: Trusting AI to handle phase transition with state update block
[2025-06-17 15:08:17,057] INFO - __main__ - main.py:7140 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯 ENHANCED PHASE CALCULATION: diagnostic_start_probe → diagnostic_probing_L5_ask_q1 (interaction 1)
[2025-06-17 15:08:17,057] WARNING - __main__ - main.py:7143 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:08:17,057] WARNING - __main__ - main.py:7144 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Input phase from context: 'diagnostic_start_probe'
[2025-06-17 15:08:17,058] WARNING - __main__ - main.py:7145 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - User query: 'Yes I'm ready'
[2025-06-17 15:08:17,058] WARNING - __main__ - main.py:7146 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Is actual student response: True
[2025-06-17 15:08:17,059] WARNING - __main__ - main.py:7147 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Current probing level: 5
[2025-06-17 15:08:17,059] WARNING - __main__ - main.py:7148 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Current question index: 0
[2025-06-17 15:08:17,059] WARNING - __main__ - main.py:7149 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Student answers count: 2
[2025-06-17 15:08:17,060] INFO - __main__ - main.py:7152 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:08:17,060] INFO - __main__ - main.py:7153 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧   - lesson_phase_from_context: 'diagnostic_start_probe'
[2025-06-17 15:08:17,060] INFO - __main__ - main.py:7154 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:17,061] INFO - __main__ - main.py:7155 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧   - user_query: 'Yes I'm ready'
[2025-06-17 15:08:17,061] INFO - __main__ - main.py:7156 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:08:17,062] WARNING - __main__ - main.py:7159 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:08:17,063] WARNING - __main__ - main.py:7160 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯   - Current phase: 'diagnostic_start_probe'
[2025-06-17 15:08:17,064] WARNING - __main__ - main.py:7161 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯   - Calculated new phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:17,065] WARNING - __main__ - main.py:7162 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯   - Phase changed: True
[2025-06-17 15:08:17,065] WARNING - __main__ - main.py:7165 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:08:17,065] WARNING - __main__ - main.py:7166 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯   - In diagnostic phase: True
[2025-06-17 15:08:17,066] WARNING - __main__ - main.py:7167 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯   - Student answers stored: {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}
[2025-06-17 15:08:17,066] WARNING - __main__ - main.py:7168 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:08:17,066] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:08:17,067] INFO - __main__ - main.py:7229 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_ask_q1', q_index='0', total_q_asked='1'
[2025-06-17 15:08:17,067] INFO - __main__ - main.py:7235 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:08:17,067] WARNING - __main__ - main.py:7260 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🚀 DIAGNOSTIC PHASE OVERRIDE: AI will receive 'diagnostic_probing_L5_ask_q1' instead of 'diagnostic_start_probe'
[2025-06-17 15:08:17,068] WARNING - __main__ - main.py:7261 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🚀 REASON: Student responded to diagnostic_start_probe, need to progress to Q1
[2025-06-17 15:08:17,068] INFO - __main__ - main.py:7340 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:08:17,068] INFO - __main__ - main.py:7351 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:08:17,069] INFO - __main__ - main.py:7357 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Template placeholder successfully substituted
[2025-06-17 15:08:17,069] INFO - __main__ - main.py:7361 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Template contains calculated phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:17,070] INFO - __main__ - main.py:7405 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Prompt truncated from 12322 to 10602 chars for performance
[2025-06-17 15:08:17,070] INFO - __main__ - main.py:7408 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] CONTENT QUALITY DEBUG:
[2025-06-17 15:08:17,070] INFO - __main__ - main.py:7409 -   - Final prompt length: 10602 characters
[2025-06-17 15:08:17,071] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:08:17,071] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:08:17,071] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:08:17,072] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:08:17,072] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_start_probe
[2025-06-17 15:08:17,072] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:08:17,072] DEBUG - __main__ - main.py:7416 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_ask_q1
• You MUST use this exact phase name in yo...
[2025-06-17 15:08:17,073] INFO - __main__ - main.py:7444 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Gemini API call attempt 1/3
[2025-06-17 15:08:18,426] INFO - __main__ - main.py:7462 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:08:18,427] INFO - __main__ - main.py:7481 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Gemini API call completed in 1.35s
[2025-06-17 15:08:18,428] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 434 characters
[2025-06-17 15:08:18,428] INFO - __main__ - main.py:7704 - [2772f8b4-d752-4bc5-a615-cf4a1b335491][enhance_lesson_content] AI response: Great! Let's start with our first question about States of Matter:  Imagine you're playing with a water balloon.  Describe how the water changes state if the balloon bursts on a hot sunny day in Niger...
[2025-06-17 15:08:18,429] INFO - __main__ - main.py:7732 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:08:18,430] INFO - __main__ - main.py:7733 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 STATE DEBUG: AI response length: 434 chars
[2025-06-17 15:08:18,431] INFO - __main__ - main.py:7745 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1}
[2025-06-17 15:08:18,432] INFO - __main__ - main.py:7747 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1}
[2025-06-17 15:08:18,433] INFO - __main__ - main.py:7811 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:08:18,433] INFO - __main__ - main.py:7817 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:08:18,434] INFO - __main__ - main.py:7837 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:08:18,435] INFO - __main__ - main.py:7841 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Auto-corrected: added missing current_question_index
[2025-06-17 15:08:18,435] INFO - __main__ - main.py:7867 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 15:08:18,436] INFO - __main__ - main.py:7879 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:08:18,437] INFO - __main__ - main.py:7922 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:08:18,437] INFO - __main__ - main.py:7931 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 15:08:18,437] INFO - __main__ - main.py:7989 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=2/5
[2025-06-17 15:08:18,438] INFO - __main__ - main.py:7992 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 📝 DIAGNOSTIC PROGRESSION: Need 3 more answers
[2025-06-17 15:08:18,438] INFO - __main__ - main.py:7994 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:08:18,438] INFO - __main__ - main.py:7998 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:08:18,439] INFO - __main__ - main.py:8030 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_start_probe → diagnostic_probing_L5_ask_q1 - trusting AI
[2025-06-17 15:08:18,439] INFO - __main__ - main.py:8075 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,439] WARNING - __main__ - main.py:8113 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 15:08:18,440] WARNING - __main__ - main.py:8114 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Phase: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,440] WARNING - __main__ - main.py:8115 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Question Index: 0
[2025-06-17 15:08:18,440] WARNING - __main__ - main.py:8116 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   - Probing Level: 5
[2025-06-17 15:08:18,441] INFO - __main__ - main.py:8120 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 15:08:18,441] INFO - __main__ - main.py:8121 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_ask_q1, Enforced phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,442] INFO - __main__ - main.py:8141 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:08:18,442] INFO - __main__ - main.py:8147 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Phase transition: diagnostic_start_probe -> diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,442] INFO - __main__ - main.py:3972 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,442] INFO - __main__ - main.py:4074 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,443] INFO - __main__ - main.py:8223 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:18,443] INFO - __main__ - main.py:8259 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:08:18,443] INFO - __main__ - main.py:8270 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧 FORCED personalization injection
[2025-06-17 15:08:18,443] INFO - __main__ - main.py:8281 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:18,444] INFO - __main__ - main.py:8284 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:08:18,444] WARNING - __main__ - main.py:8305 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:08:18,444] WARNING - __main__ - main.py:8306 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔄   - AI provided state update: True
[2025-06-17 15:08:18,445] WARNING - __main__ - main.py:8308 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔄   - AI proposed phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:18,446] WARNING - __main__ - main.py:8309 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔄   - Current lesson phase: 'diagnostic_start_probe'
[2025-06-17 15:08:18,447] WARNING - __main__ - main.py:8310 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔄   - Python calculated phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:18,448] INFO - __main__ - main.py:8318 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:08:18,449] INFO - __main__ - main.py:8319 -   - Total execution time: 1.398s
[2025-06-17 15:08:18,449] WARNING - __main__ - main.py:5643 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:08:18,450] WARNING - __main__ - main.py:5644 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖   - Content length: 310 chars
[2025-06-17 15:08:18,450] WARNING - __main__ - main.py:5645 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:18,450] WARNING - __main__ - main.py:5646 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🤖   - Raw state block: None...
[2025-06-17 15:08:18,451] INFO - __main__ - main.py:5662 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:08:18,451] INFO - __main__ - main.py:5663 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_ask_q1, Session=diagnostic_start_probe, Final=diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,833] INFO - __main__ - main.py:5772 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI processing completed in 1.79s
[2025-06-17 15:08:18,833] WARNING - __main__ - main.py:5783 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:18,834] INFO - __main__ - main.py:3897 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI state update validation passed: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,835] WARNING - __main__ - main.py:5792 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:08:18,836] WARNING - __main__ - main.py:5797 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔄 PHASE TRANSITION: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,837] WARNING - __main__ - main.py:5806 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:08:18,837] WARNING - __main__ - main.py:5807 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-17 15:08:18,838] WARNING - __main__ - main.py:5808 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:08:18,839] WARNING - __main__ - main.py:5809 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:18,840] WARNING - __main__ - main.py:5810 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔍   4. Final phase to save: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:18,841] WARNING - __main__ - main.py:5813 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:08:18,841] WARNING - __main__ - main.py:5814 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-17 15:08:18,842] WARNING - __main__ - main.py:5815 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:08:18,843] WARNING - __main__ - main.py:5816 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 💾   - Final phase to save: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:08:18,843] WARNING - __main__ - main.py:5817 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 💾   - Phase change: True
[2025-06-17 15:08:18,844] INFO - __main__ - main.py:3929 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:08:18,844] INFO - __main__ - main.py:3930 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Phase transition: diagnostic_start_probe -> diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,845] INFO - __main__ - main.py:3931 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Current level: 5
[2025-06-17 15:08:18,846] INFO - __main__ - main.py:3932 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Question index: 0
[2025-06-17 15:08:18,848] INFO - __main__ - main.py:3933 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   First encounter: True
[2025-06-17 15:08:18,849] INFO - __main__ - main.py:3938 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Answers collected: 2
[2025-06-17 15:08:18,850] INFO - __main__ - main.py:3939 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Levels failed: 0
[2025-06-17 15:08:18,850] INFO - __main__ - main.py:3897 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI state update validation passed: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:18,850] INFO - __main__ - main.py:3943 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   State update valid: True
[2025-06-17 15:08:18,851] INFO - __main__ - main.py:3950 - [2772f8b4-d752-4bc5-a615-cf4a1b335491]   Diagnostic complete: False
[2025-06-17 15:08:18,851] WARNING - __main__ - main.py:5829 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:08:19,335] WARNING - __main__ - main.py:5856 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:08:19,336] WARNING - __main__ - main.py:5857 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:19,338] WARNING - __main__ - main.py:5858 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Probing Level: 5
[2025-06-17 15:08:19,339] WARNING - __main__ - main.py:5859 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Question Index: 0
[2025-06-17 15:08:19,340] WARNING - __main__ - main.py:5860 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Diagnostic Complete: False
[2025-06-17 15:08:20,224] INFO - __main__ - main.py:5919 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:08:20,225] WARNING - __main__ - main.py:5920 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:08:20,225] WARNING - __main__ - main.py:5921 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:08:20,226] WARNING - __main__ - main.py:5922 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Phase transition: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 15:08:20,226] WARNING - __main__ - main.py:5923 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] ✅   - Interaction logged successfully
[2025-06-17 15:08:20,227] INFO - __main__ - main.py:9216 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:08:20,228] DEBUG - __main__ - main.py:2753 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:08:20,228] DEBUG - __main__ - main.py:5969 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] No final assessment data found in AI response
[2025-06-17 15:08:20,232] INFO - __main__ - main.py:6034 - [2772f8b4-d752-4bc5-a615-cf4a1b335491] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:08:20,233] WARNING - __main__ - main.py:625 - High response time detected: 5.95s for enhance_content_api
[2025-06-17 15:08:20,233] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:08:20] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 15:09:01,785] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:09:01,786] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "1db15274-fa11-424c-8c13-b9c1f681cad0", "timestamp": "2025-06-17T14:09:01.785891+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your Basic Science and Technology tutor, and I'm excited to help you learn about States of Matter. We'll start with a short diagnostic assessment to understand your current knowledge. This helps me tailor the best learning experience for you. Are you ready to begin with some questions about Discover, Three, States, Matter, and Transformation?", "timestamp": "2025-06-17T14:08:05.816Z"}, {"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T14:08:14.157Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about States of Matter: Imagine you're playing with a water balloon. Describe how the water changes state if the balloon bursts on a hot sunny day in Nigeria. Think about what happens to the water and what state it's in before and after the balloon breaks.", "timestamp": "2025-06-17T14:08:20.246Z"}]}}
[2025-06-17 15:09:01,788] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Using temporary auth bypass with mock token
[2025-06-17 15:09:01,789] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:09:01,793] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:09:01,795] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:09:01,796] INFO - __main__ - main.py:5180 - [enhance_content_api][1db15274-fa11-424c-8c13-b9c1f681cad0] Processing enhance content request
[2025-06-17 15:09:01,799] INFO - __main__ - main.py:5226 - [1db15274-fa11-424c-8c13-b9c1f681cad0][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the wate
[2025-06-17 15:09:02,158] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:09:02,159] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:09:02,159] INFO - __main__ - main.py:5272 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:09:02,160] INFO - __main__ - main.py:5311 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:09:02,160] INFO - __main__ - main.py:2273 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:09:02,638] INFO - __main__ - main.py:2332 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:09:02,638] INFO - __main__ - main.py:2332 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:09:02,639] INFO - __main__ - main.py:2332 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:09:02,639] INFO - __main__ - main.py:2332 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:09:02,639] INFO - __main__ - main.py:2332 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:09:02,639] INFO - __main__ - main.py:2401 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:09:02,640] DEBUG - __main__ - main.py:2415 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:09:02,641] DEBUG - __main__ - main.py:2418 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:09:02,641] DEBUG - __main__ - main.py:2419 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:09:02,642] DEBUG - __main__ - main.py:2420 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:09:02,643] INFO - __main__ - main.py:2424 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:09:03,165] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:09:03,166] INFO - __main__ - main.py:2434 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Gemini API call completed in 0.52s. Raw response: 'materials'
[2025-06-17 15:09:03,166] DEBUG - __main__ - main.py:2456 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:09:03,167] INFO - __main__ - main.py:2461 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:09:03,168] INFO - __main__ - main.py:5344 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Successfully inferred module ID via AI: materials
[2025-06-17 15:09:03,169] INFO - __main__ - main.py:5370 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:09:03,458] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:09:04,051] WARNING - __main__ - main.py:5391 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:09:04,051] WARNING - __main__ - main.py:5392 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Session exists: True
[2025-06-17 15:09:04,052] WARNING - __main__ - main.py:5393 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Current phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,053] WARNING - __main__ - main.py:5394 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'diagnostic_completed_this_session', 'current_probing_level_number', 'lesson_context_snapshot', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 15:09:04,055] INFO - __main__ - main.py:5458 - [1db15274-fa11-424c-8c13-b9c1f681cad0] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:09:04,056] INFO - __main__ - main.py:5459 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   assigned_level_for_teaching (session): None
[2025-06-17 15:09:04,057] INFO - __main__ - main.py:5460 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   latest_assessed_level (profile): None
[2025-06-17 15:09:04,057] INFO - __main__ - main.py:5461 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   teaching_level_for_returning_student: None
[2025-06-17 15:09:04,057] INFO - __main__ - main.py:5462 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   has_completed_diagnostic_before: False
[2025-06-17 15:09:04,058] INFO - __main__ - main.py:5463 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   is_first_encounter_for_module: True
[2025-06-17 15:09:04,058] WARNING - __main__ - main.py:5468 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:09:04,058] INFO - __main__ - main.py:5474 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:09:04,059] INFO - __main__ - main.py:5475 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Retrieved from Firestore: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,059] INFO - __main__ - main.py:5476 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:09:04,060] INFO - __main__ - main.py:5477 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Is first encounter: True
[2025-06-17 15:09:04,060] INFO - __main__ - main.py:5478 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Diagnostic completed: False
[2025-06-17 15:09:04,060] INFO - __main__ - main.py:5484 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,061] INFO - __main__ - main.py:5498 - [1db15274-fa11-424c-8c13-b9c1f681cad0] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:09:04,061] INFO - __main__ - main.py:5500 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Final phase for AI logic: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,061] INFO - __main__ - main.py:5520 - [1db15274-fa11-424c-8c13-b9c1f681cad0] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-17 15:09:04,062] INFO - __main__ - main.py:3692 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Diagnostic context validation passed
[2025-06-17 15:09:04,063] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,065] WARNING - __main__ - main.py:5586 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_ask_q1' for first encounter
[2025-06-17 15:09:04,068] INFO - __main__ - main.py:3802 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Enhanced diagnostic context with 35 fields
[2025-06-17 15:09:04,068] INFO - __main__ - main.py:5608 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,069] DEBUG - __main__ - main.py:5609 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:09:04,069] WARNING - __main__ - main.py:5617 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:09:04,069] WARNING - __main__ - main.py:5618 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:09:04,070] WARNING - __main__ - main.py:5619 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖   - Student query: Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy...
[2025-06-17 15:09:04,070] WARNING - __main__ - main.py:5620 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:09:04,071] INFO - __main__ - main.py:6493 - [1db15274-fa11-424c-8c13-b9c1f681cad0] enhance_lesson_content invoked. Query: 'Hi! This sounds like fun!  Before the balloon burs...'
[2025-06-17 15:09:04,071] INFO - __main__ - main.py:6536 - [1db15274-fa11-424c-8c13-b9c1f681cad0] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_ask_q1', processed = 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,071] INFO - __main__ - main.py:6547 - [1db15274-fa11-424c-8c13-b9c1f681cad0][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_ask_q1', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:09:04,072] INFO - __main__ - main.py:6581 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC ANSWER: Storing sequential answer 3
[2025-06-17 15:09:04,072] INFO - __main__ - main.py:6586 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC ANSWER STORED: q3 = 'Hi! This sounds like fun!  Before the balloon burs...'
[2025-06-17 15:09:04,072] INFO - __main__ - main.py:6587 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Total diagnostic answers now: 3/5
[2025-06-17 15:09:04,073] INFO - __main__ - main.py:6628 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 📝 DIAGNOSTIC PROGRESSION: Continue with 3/5 answers, phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,073] INFO - __main__ - main.py:6766 - [1db15274-fa11-424c-8c13-b9c1f681cad0][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,073] INFO - __main__ - main.py:6773 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,074] INFO - __main__ - main.py:6774 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Questions asked: 3/5
[2025-06-17 15:09:04,074] INFO - __main__ - main.py:6775 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 15:09:04,074] INFO - __main__ - main.py:6776 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Student answers count: 3
[2025-06-17 15:09:04,075] INFO - __main__ - main.py:6781 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Answer q1: Start diagnostic assessment...
[2025-06-17 15:09:04,075] INFO - __main__ - main.py:6781 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 15:09:04,075] INFO - __main__ - main.py:6781 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] Answer q3: Hi! This sounds like fun!  Before the balloon burs...
[2025-06-17 15:09:04,076] INFO - __main__ - main.py:6805 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_ask_q1, Answers=3/5
[2025-06-17 15:09:04,076] INFO - __main__ - main.py:6808 - [1db15274-fa11-424c-8c13-b9c1f681cad0][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:09:04,076] INFO - __main__ - main.py:6839 - [1db15274-fa11-424c-8c13-b9c1f681cad0] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:09:04,077] INFO - __main__ - main.py:6853 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,077] INFO - __main__ - main.py:6861 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,077] INFO - __main__ - main.py:7023 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC PROGRESSION: Could not parse question number - staying in diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,078] INFO - __main__ - main.py:7140 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_ask_q1 (interaction 1)
[2025-06-17 15:09:04,078] WARNING - __main__ - main.py:7143 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:09:04,079] WARNING - __main__ - main.py:7144 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Input phase from context: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,080] WARNING - __main__ - main.py:7145 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - User query: 'Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!'
[2025-06-17 15:09:04,082] WARNING - __main__ - main.py:7146 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Is actual student response: True
[2025-06-17 15:09:04,083] WARNING - __main__ - main.py:7147 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Current probing level: 5
[2025-06-17 15:09:04,083] WARNING - __main__ - main.py:7148 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Current question index: 0
[2025-06-17 15:09:04,083] WARNING - __main__ - main.py:7149 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   - Student answers count: 3
[2025-06-17 15:09:04,084] INFO - __main__ - main.py:7152 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:09:04,084] INFO - __main__ - main.py:7153 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,084] INFO - __main__ - main.py:7154 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,085] INFO - __main__ - main.py:7155 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧   - user_query: 'Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!'
[2025-06-17 15:09:04,085] INFO - __main__ - main.py:7156 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:09:04,086] WARNING - __main__ - main.py:7159 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:09:04,086] WARNING - __main__ - main.py:7160 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯   - Current phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,086] WARNING - __main__ - main.py:7161 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯   - Calculated new phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:04,087] WARNING - __main__ - main.py:7162 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯   - Phase changed: False
[2025-06-17 15:09:04,087] WARNING - __main__ - main.py:7165 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:09:04,087] WARNING - __main__ - main.py:7166 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯   - In diagnostic phase: True
[2025-06-17 15:09:04,088] WARNING - __main__ - main.py:7167 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯   - Student answers stored: {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready", 'q3': "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!"}
[2025-06-17 15:09:04,088] WARNING - __main__ - main.py:7168 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:09:04,088] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:09:04,089] INFO - __main__ - main.py:7229 - [1db15274-fa11-424c-8c13-b9c1f681cad0][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_ask_q1', q_index='0', total_q_asked='2'
[2025-06-17 15:09:04,089] INFO - __main__ - main.py:7235 - [1db15274-fa11-424c-8c13-b9c1f681cad0][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:09:04,090] INFO - __main__ - main.py:7340 - [1db15274-fa11-424c-8c13-b9c1f681cad0] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:09:04,090] INFO - __main__ - main.py:7351 - [1db15274-fa11-424c-8c13-b9c1f681cad0] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:09:04,091] INFO - __main__ - main.py:7357 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Template placeholder successfully substituted
[2025-06-17 15:09:04,091] INFO - __main__ - main.py:7361 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Template contains calculated phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,091] INFO - __main__ - main.py:7405 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Prompt truncated from 13130 to 10602 chars for performance
[2025-06-17 15:09:04,092] INFO - __main__ - main.py:7408 - [1db15274-fa11-424c-8c13-b9c1f681cad0] CONTENT QUALITY DEBUG:
[2025-06-17 15:09:04,092] INFO - __main__ - main.py:7409 -   - Final prompt length: 10602 characters
[2025-06-17 15:09:04,093] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:09:04,093] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:09:04,093] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:09:04,094] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:09:04,094] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 15:09:04,095] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:09:04,095] DEBUG - __main__ - main.py:7416 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_ask_q1
• You MUST use this exact phase name in yo...
[2025-06-17 15:09:04,097] INFO - __main__ - main.py:7444 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Gemini API call attempt 1/3
[2025-06-17 15:09:05,369] INFO - __main__ - main.py:7462 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:09:05,369] INFO - __main__ - main.py:7481 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Gemini API call completed in 1.27s
[2025-06-17 15:09:05,370] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 397 characters
[2025-06-17 15:09:05,370] INFO - __main__ - main.py:7704 - [1db15274-fa11-424c-8c13-b9c1f681cad0][enhance_lesson_content] AI response: That's a great observation, Andrea!  You correctly identified that the water in the balloon, before it bursts, is a liquid. Now, let's think about what happens to the water *after* the balloon bursts....
[2025-06-17 15:09:05,371] INFO - __main__ - main.py:7732 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:09:05,372] INFO - __main__ - main.py:7733 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 STATE DEBUG: AI response length: 397 chars
[2025-06-17 15:09:05,372] INFO - __main__ - main.py:7745 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1}
[2025-06-17 15:09:05,373] INFO - __main__ - main.py:7747 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1}
[2025-06-17 15:09:05,374] WARNING - __main__ - main.py:7806 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧 FORCING DIAGNOSTIC PROGRESSION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,375] INFO - __main__ - main.py:7811 - [1db15274-fa11-424c-8c13-b9c1f681cad0] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:09:05,375] INFO - __main__ - main.py:7817 - [1db15274-fa11-424c-8c13-b9c1f681cad0] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:09:05,376] INFO - __main__ - main.py:7837 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:09:05,376] INFO - __main__ - main.py:7867 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,377] INFO - __main__ - main.py:7879 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:09:05,377] INFO - __main__ - main.py:7922 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:09:05,377] INFO - __main__ - main.py:7931 - [1db15274-fa11-424c-8c13-b9c1f681cad0] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,378] INFO - __main__ - main.py:7989 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=3/5
[2025-06-17 15:09:05,378] INFO - __main__ - main.py:7992 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 📝 DIAGNOSTIC PROGRESSION: Need 2 more answers
[2025-06-17 15:09:05,379] INFO - __main__ - main.py:7994 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:09:05,380] INFO - __main__ - main.py:7998 - [1db15274-fa11-424c-8c13-b9c1f681cad0] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:09:05,381] INFO - __main__ - main.py:8030 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2 - trusting AI
[2025-06-17 15:09:05,381] INFO - __main__ - main.py:8141 - [1db15274-fa11-424c-8c13-b9c1f681cad0] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:09:05,382] INFO - __main__ - main.py:8147 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Phase transition: diagnostic_probing_L5_ask_q1 -> diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,383] INFO - __main__ - main.py:3972 - [1db15274-fa11-424c-8c13-b9c1f681cad0] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,383] INFO - __main__ - main.py:4074 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,384] INFO - __main__ - main.py:8223 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,384] INFO - __main__ - main.py:8259 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:09:05,385] INFO - __main__ - main.py:8275 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ AI response already includes personalization
[2025-06-17 15:09:05,385] INFO - __main__ - main.py:8281 - [1db15274-fa11-424c-8c13-b9c1f681cad0] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,385] INFO - __main__ - main.py:8284 - [1db15274-fa11-424c-8c13-b9c1f681cad0] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:09:05,386] WARNING - __main__ - main.py:8305 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:09:05,386] WARNING - __main__ - main.py:8306 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔄   - AI provided state update: True
[2025-06-17 15:09:05,386] WARNING - __main__ - main.py:8308 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:05,387] WARNING - __main__ - main.py:8309 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔄   - Current lesson phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:05,387] WARNING - __main__ - main.py:8310 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔄   - Python calculated phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:05,387] INFO - __main__ - main.py:8318 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:09:05,388] INFO - __main__ - main.py:8319 -   - Total execution time: 1.316s
[2025-06-17 15:09:05,388] WARNING - __main__ - main.py:5643 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:09:05,388] WARNING - __main__ - main.py:5644 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖   - Content length: 263 chars
[2025-06-17 15:09:05,389] WARNING - __main__ - main.py:5645 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,389] WARNING - __main__ - main.py:5646 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🤖   - Raw state block: None...
[2025-06-17 15:09:05,389] INFO - __main__ - main.py:5662 - [1db15274-fa11-424c-8c13-b9c1f681cad0] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:09:05,389] INFO - __main__ - main.py:5663 - [1db15274-fa11-424c-8c13-b9c1f681cad0] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q1_ask_q2, Session=diagnostic_probing_L5_ask_q1, Final=diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,698] INFO - __main__ - main.py:5772 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI processing completed in 1.63s
[2025-06-17 15:09:05,699] WARNING - __main__ - main.py:5783 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_ask_q1', new_phase='diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:05,699] INFO - __main__ - main.py:3897 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI state update validation passed: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,700] WARNING - __main__ - main.py:5792 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:09:05,701] WARNING - __main__ - main.py:5797 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔄 PHASE TRANSITION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,702] WARNING - __main__ - main.py:5806 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:09:05,703] WARNING - __main__ - main.py:5807 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   1. Input phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:05,703] WARNING - __main__ - main.py:5808 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:09:05,704] WARNING - __main__ - main.py:5809 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,705] WARNING - __main__ - main.py:5810 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:05,706] WARNING - __main__ - main.py:5813 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:09:05,706] WARNING - __main__ - main.py:5814 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 💾   - Current phase input: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 15:09:05,707] WARNING - __main__ - main.py:5815 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 15:09:05,707] WARNING - __main__ - main.py:5816 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:05,708] WARNING - __main__ - main.py:5817 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 💾   - Phase change: True
[2025-06-17 15:09:05,708] INFO - __main__ - main.py:3929 - [1db15274-fa11-424c-8c13-b9c1f681cad0] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:09:05,708] INFO - __main__ - main.py:3930 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Phase transition: diagnostic_probing_L5_ask_q1 -> diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,709] INFO - __main__ - main.py:3931 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Current level: 5
[2025-06-17 15:09:05,709] INFO - __main__ - main.py:3932 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Question index: 0
[2025-06-17 15:09:05,709] INFO - __main__ - main.py:3933 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   First encounter: True
[2025-06-17 15:09:05,710] INFO - __main__ - main.py:3938 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Answers collected: 3
[2025-06-17 15:09:05,710] INFO - __main__ - main.py:3939 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Levels failed: 0
[2025-06-17 15:09:05,711] INFO - __main__ - main.py:3897 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI state update validation passed: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:05,711] INFO - __main__ - main.py:3943 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   State update valid: True
[2025-06-17 15:09:05,712] INFO - __main__ - main.py:3950 - [1db15274-fa11-424c-8c13-b9c1f681cad0]   Diagnostic complete: False
[2025-06-17 15:09:05,714] WARNING - __main__ - main.py:5829 - [1db15274-fa11-424c-8c13-b9c1f681cad0] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:09:06,276] WARNING - __main__ - main.py:5856 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:09:06,277] WARNING - __main__ - main.py:5857 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:06,277] WARNING - __main__ - main.py:5858 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Probing Level: 5
[2025-06-17 15:09:06,278] WARNING - __main__ - main.py:5859 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Question Index: 1
[2025-06-17 15:09:06,280] WARNING - __main__ - main.py:5860 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Diagnostic Complete: False
[2025-06-17 15:09:07,149] INFO - __main__ - main.py:5919 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:09:07,150] WARNING - __main__ - main.py:5920 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:09:07,150] WARNING - __main__ - main.py:5921 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:09:07,151] WARNING - __main__ - main.py:5922 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Phase transition: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:07,152] WARNING - __main__ - main.py:5923 - [1db15274-fa11-424c-8c13-b9c1f681cad0] ✅   - Interaction logged successfully
[2025-06-17 15:09:07,153] INFO - __main__ - main.py:9216 - [1db15274-fa11-424c-8c13-b9c1f681cad0] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:09:07,153] DEBUG - __main__ - main.py:2753 - [1db15274-fa11-424c-8c13-b9c1f681cad0] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:09:07,154] DEBUG - __main__ - main.py:5969 - [1db15274-fa11-424c-8c13-b9c1f681cad0] No final assessment data found in AI response
[2025-06-17 15:09:07,157] INFO - __main__ - main.py:6034 - [1db15274-fa11-424c-8c13-b9c1f681cad0] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:09:07,158] WARNING - __main__ - main.py:625 - High response time detected: 5.37s for enhance_content_api
[2025-06-17 15:09:07,159] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:09:07] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 15:09:34,870] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:09:34,871] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "61652124-c0e1-4dd5-bb4f-7ed17fd907db", "timestamp": "2025-06-17T14:09:34.870953+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your Basic Science and Technology tutor, and I'm excited to help you learn about States of Matter. We'll start with a short diagnostic assessment to understand your current knowledge. This helps me tailor the best learning experience for you. Are you ready to begin with some questions about Discover, Three, States, Matter, and Transformation?", "timestamp": "2025-06-17T14:08:05.816Z"}, {"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T14:08:14.157Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about States of Matter: Imagine you're playing with a water balloon. Describe how the water changes state if the balloon bursts on a hot sunny day in Nigeria. Think about what happens to the water and what state it's in before and after the balloon breaks.", "timestamp": "2025-06-17T14:08:20.246Z"}, {"role": "user", "content": "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!", "timestamp": "2025-06-17T14:09:01.546Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! You correctly identified that the water in the balloon, before it bursts, is a liquid. Now, let's think about what happens to the water *after* the balloon bursts. Does the state of the water change, or does it remain a liquid?", "timestamp": "2025-06-17T14:09:07.180Z"}]}}
[2025-06-17 15:09:34,872] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Using temporary auth bypass with mock token
[2025-06-17 15:09:34,873] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:09:34,886] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:09:34,887] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:09:34,888] INFO - __main__ - main.py:5180 - [enhance_content_api][61652124-c0e1-4dd5-bb4f-7ed17fd907db] Processing enhance content request
[2025-06-17 15:09:34,889] INFO - __main__ - main.py:5226 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the ballo
[2025-06-17 15:09:35,200] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:09:35,201] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:09:35,201] INFO - __main__ - main.py:5272 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:09:35,203] INFO - __main__ - main.py:5311 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:09:35,204] INFO - __main__ - main.py:2273 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:09:35,732] INFO - __main__ - main.py:2332 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:09:35,733] INFO - __main__ - main.py:2332 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:09:35,733] INFO - __main__ - main.py:2332 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:09:35,733] INFO - __main__ - main.py:2332 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:09:35,734] INFO - __main__ - main.py:2332 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:09:35,734] INFO - __main__ - main.py:2401 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:09:35,735] DEBUG - __main__ - main.py:2415 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:09:35,736] DEBUG - __main__ - main.py:2418 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:09:35,736] DEBUG - __main__ - main.py:2419 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:09:35,737] DEBUG - __main__ - main.py:2420 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:09:35,738] INFO - __main__ - main.py:2424 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:09:36,276] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:09:36,277] INFO - __main__ - main.py:2434 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Gemini API call completed in 0.54s. Raw response: 'materials'
[2025-06-17 15:09:36,277] DEBUG - __main__ - main.py:2456 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:09:36,278] INFO - __main__ - main.py:2461 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:09:36,279] INFO - __main__ - main.py:5344 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Successfully inferred module ID via AI: materials
[2025-06-17 15:09:36,280] INFO - __main__ - main.py:5370 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:09:36,567] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:09:37,185] WARNING - __main__ - main.py:5391 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:09:37,185] WARNING - __main__ - main.py:5392 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Session exists: True
[2025-06-17 15:09:37,186] WARNING - __main__ - main.py:5393 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Current phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,187] WARNING - __main__ - main.py:5394 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'lesson_context_snapshot', 'student_name', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 15:09:37,188] INFO - __main__ - main.py:5458 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:09:37,189] INFO - __main__ - main.py:5459 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   assigned_level_for_teaching (session): None
[2025-06-17 15:09:37,190] INFO - __main__ - main.py:5460 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   latest_assessed_level (profile): None
[2025-06-17 15:09:37,191] INFO - __main__ - main.py:5461 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   teaching_level_for_returning_student: None
[2025-06-17 15:09:37,191] INFO - __main__ - main.py:5462 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   has_completed_diagnostic_before: False
[2025-06-17 15:09:37,192] INFO - __main__ - main.py:5463 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   is_first_encounter_for_module: True
[2025-06-17 15:09:37,192] WARNING - __main__ - main.py:5468 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:09:37,193] INFO - __main__ - main.py:5474 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:09:37,193] INFO - __main__ - main.py:5475 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,194] INFO - __main__ - main.py:5476 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:09:37,194] INFO - __main__ - main.py:5477 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Is first encounter: True
[2025-06-17 15:09:37,195] INFO - __main__ - main.py:5478 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Diagnostic completed: False
[2025-06-17 15:09:37,195] INFO - __main__ - main.py:5484 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,197] INFO - __main__ - main.py:5498 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:09:37,199] INFO - __main__ - main.py:5500 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Final phase for AI logic: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,200] INFO - __main__ - main.py:5514 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] NEW SESSION: Using phase-based question index 0 from phase diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,200] INFO - __main__ - main.py:3692 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Diagnostic context validation passed
[2025-06-17 15:09:37,201] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,201] WARNING - __main__ - main.py:5586 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q1_ask_q2' for first encounter
[2025-06-17 15:09:37,201] INFO - __main__ - main.py:3802 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Enhanced diagnostic context with 35 fields
[2025-06-17 15:09:37,202] INFO - __main__ - main.py:5608 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,202] DEBUG - __main__ - main.py:5609 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:09:37,202] WARNING - __main__ - main.py:5617 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:09:37,203] WARNING - __main__ - main.py:5618 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:09:37,203] WARNING - __main__ - main.py:5619 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖   - Student query: That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a...
[2025-06-17 15:09:37,203] WARNING - __main__ - main.py:5620 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:09:37,205] INFO - __main__ - main.py:6493 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] enhance_lesson_content invoked. Query: 'That's a good question!  Right after the water bal...'
[2025-06-17 15:09:37,205] INFO - __main__ - main.py:6536 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q1_ask_q2', processed = 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,205] INFO - __main__ - main.py:6547 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q1_ask_q2', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:09:37,206] INFO - __main__ - main.py:6577 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC ANSWER: Storing Q1 answer from phase diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,206] INFO - __main__ - main.py:6586 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC ANSWER STORED: q1 = 'That's a good question!  Right after the water bal...'
[2025-06-17 15:09:37,206] INFO - __main__ - main.py:6587 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Total diagnostic answers now: 2/5
[2025-06-17 15:09:37,206] INFO - __main__ - main.py:6628 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 📝 DIAGNOSTIC PROGRESSION: Continue with 2/5 answers, phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,207] INFO - __main__ - main.py:6766 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,207] INFO - __main__ - main.py:6773 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,207] INFO - __main__ - main.py:6774 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] Questions asked: 2/5
[2025-06-17 15:09:37,208] INFO - __main__ - main.py:6775 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 15:09:37,208] INFO - __main__ - main.py:6776 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] Student answers count: 2
[2025-06-17 15:09:37,208] INFO - __main__ - main.py:6781 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] Answer q1: That's a good question!  Right after the water bal...
[2025-06-17 15:09:37,208] INFO - __main__ - main.py:6781 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 15:09:37,209] INFO - __main__ - main.py:6805 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q1_ask_q2, Answers=2/5
[2025-06-17 15:09:37,209] INFO - __main__ - main.py:6808 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:09:37,209] INFO - __main__ - main.py:6839 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:09:37,209] INFO - __main__ - main.py:6853 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,210] INFO - __main__ - main.py:6861 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,211] INFO - __main__ - main.py:6906 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 DIAGNOSTIC EVAL DEBUG: Q2 response analysis:
[2025-06-17 15:09:37,211] INFO - __main__ - main.py:6907 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 DIAGNOSTIC EVAL DEBUG:   - user_query: 'That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.'
[2025-06-17 15:09:37,212] INFO - __main__ - main.py:6908 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 DIAGNOSTIC EVAL DEBUG:   - Final result: True
[2025-06-17 15:09:37,214] INFO - __main__ - main.py:6922 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC PROGRESSION: Q2 answered → Q3 (diagnostic_probing_L5_eval_q2_ask_q3)
[2025-06-17 15:09:37,216] WARNING - __main__ - main.py:6933 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧 DIAGNOSTIC INDEX UPDATE: Updated current_q_index_for_prompt to 2 and context to 2
[2025-06-17 15:09:37,216] INFO - __main__ - main.py:7140 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3 (interaction 1)
[2025-06-17 15:09:37,217] WARNING - __main__ - main.py:7143 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:09:37,217] WARNING - __main__ - main.py:7144 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,218] WARNING - __main__ - main.py:7145 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - User query: 'That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.'
[2025-06-17 15:09:37,218] WARNING - __main__ - main.py:7146 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Is actual student response: True
[2025-06-17 15:09:37,219] WARNING - __main__ - main.py:7147 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Current probing level: 5
[2025-06-17 15:09:37,219] WARNING - __main__ - main.py:7148 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Current question index: 2
[2025-06-17 15:09:37,219] WARNING - __main__ - main.py:7149 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Student answers count: 2
[2025-06-17 15:09:37,220] INFO - __main__ - main.py:7152 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:09:37,220] INFO - __main__ - main.py:7153 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,220] INFO - __main__ - main.py:7154 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:37,221] INFO - __main__ - main.py:7155 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧   - user_query: 'That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.'
[2025-06-17 15:09:37,221] INFO - __main__ - main.py:7156 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:09:37,221] WARNING - __main__ - main.py:7159 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:09:37,222] WARNING - __main__ - main.py:7160 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,222] WARNING - __main__ - main.py:7161 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯   - Calculated new phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:37,223] WARNING - __main__ - main.py:7162 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯   - Phase changed: True
[2025-06-17 15:09:37,223] WARNING - __main__ - main.py:7165 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:09:37,223] WARNING - __main__ - main.py:7166 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯   - In diagnostic phase: True
[2025-06-17 15:09:37,224] WARNING - __main__ - main.py:7167 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯   - Student answers stored: {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': "Yes I'm ready"}
[2025-06-17 15:09:37,225] WARNING - __main__ - main.py:7168 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:09:37,225] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:09:37,226] INFO - __main__ - main.py:7229 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_eval_q2_ask_q3', q_index='2', total_q_asked='2'
[2025-06-17 15:09:37,226] INFO - __main__ - main.py:7235 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:09:37,226] WARNING - __main__ - main.py:7260 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🚀 DIAGNOSTIC PHASE OVERRIDE: AI will receive 'diagnostic_probing_L5_eval_q2_ask_q3' instead of 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:37,227] WARNING - __main__ - main.py:7261 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🚀 REASON: Student responded to diagnostic_start_probe, need to progress to Q1
[2025-06-17 15:09:37,228] INFO - __main__ - main.py:7340 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:09:37,228] INFO - __main__ - main.py:7351 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:09:37,230] INFO - __main__ - main.py:7357 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Template placeholder successfully substituted
[2025-06-17 15:09:37,231] INFO - __main__ - main.py:7361 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Template contains calculated phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:37,233] INFO - __main__ - main.py:7405 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Prompt truncated from 13839 to 10642 chars for performance
[2025-06-17 15:09:37,233] INFO - __main__ - main.py:7408 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] CONTENT QUALITY DEBUG:
[2025-06-17 15:09:37,233] INFO - __main__ - main.py:7409 -   - Final prompt length: 10642 characters
[2025-06-17 15:09:37,234] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:09:37,234] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:09:37,235] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:09:37,235] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:09:37,236] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 15:09:37,236] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:09:37,236] DEBUG - __main__ - main.py:7416 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_eval_q2_ask_q3", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_eval_q2_ask_q3
• You MUST use this exact ...
[2025-06-17 15:09:37,237] INFO - __main__ - main.py:7444 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Gemini API call attempt 1/3
[2025-06-17 15:09:38,714] INFO - __main__ - main.py:7462 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:09:38,714] INFO - __main__ - main.py:7481 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Gemini API call completed in 1.48s
[2025-06-17 15:09:38,715] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 505 characters
[2025-06-17 15:09:38,716] INFO - __main__ - main.py:7704 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db][enhance_lesson_content] AI response: That's a great observation, Andrea!  It shows you understand that a change in state requires more than just a change in its container. Now let's explore another situation. Imagine you're playing a gam...
[2025-06-17 15:09:38,717] INFO - __main__ - main.py:7732 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:09:38,718] INFO - __main__ - main.py:7733 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 STATE DEBUG: AI response length: 505 chars
[2025-06-17 15:09:38,719] INFO - __main__ - main.py:7745 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_eval_q2_ask_q3", "interaction_count": 1}
[2025-06-17 15:09:38,720] INFO - __main__ - main.py:7747 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'interaction_count': 1}
[2025-06-17 15:09:38,721] INFO - __main__ - main.py:7811 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:09:38,722] INFO - __main__ - main.py:7817 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:09:38,723] INFO - __main__ - main.py:7837 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:09:38,723] INFO - __main__ - main.py:7841 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Auto-corrected: added missing current_question_index
[2025-06-17 15:09:38,724] INFO - __main__ - main.py:7867 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 2}
[2025-06-17 15:09:38,724] INFO - __main__ - main.py:7879 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:09:38,725] INFO - __main__ - main.py:7922 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:09:38,725] INFO - __main__ - main.py:7931 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 2}
[2025-06-17 15:09:38,726] INFO - __main__ - main.py:7989 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=2/5
[2025-06-17 15:09:38,727] INFO - __main__ - main.py:7992 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 📝 DIAGNOSTIC PROGRESSION: Need 3 more answers
[2025-06-17 15:09:38,727] INFO - __main__ - main.py:7994 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:09:38,728] INFO - __main__ - main.py:7998 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:09:38,728] INFO - __main__ - main.py:8030 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3 - trusting AI
[2025-06-17 15:09:38,729] INFO - __main__ - main.py:8075 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:38,731] WARNING - __main__ - main.py:8113 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 15:09:38,732] WARNING - __main__ - main.py:8114 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Phase: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:38,733] WARNING - __main__ - main.py:8115 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Question Index: 2
[2025-06-17 15:09:38,734] WARNING - __main__ - main.py:8116 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   - Probing Level: 5
[2025-06-17 15:09:38,734] INFO - __main__ - main.py:8120 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 15:09:38,735] INFO - __main__ - main.py:8121 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_eval_q2_ask_q3, Enforced phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:38,735] INFO - __main__ - main.py:8141 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:09:38,735] INFO - __main__ - main.py:8147 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 -> diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:38,736] INFO - __main__ - main.py:3972 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:38,736] INFO - __main__ - main.py:4074 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:38,737] INFO - __main__ - main.py:8223 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'current_probing_level_number': 5, 'current_question_index': 2, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:09:38,738] INFO - __main__ - main.py:8259 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:09:38,738] INFO - __main__ - main.py:8275 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ AI response already includes personalization
[2025-06-17 15:09:38,738] INFO - __main__ - main.py:8281 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'current_probing_level_number': 5, 'current_question_index': 2, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:09:38,739] INFO - __main__ - main.py:8284 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:09:38,739] WARNING - __main__ - main.py:8305 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:09:38,739] WARNING - __main__ - main.py:8306 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔄   - AI provided state update: True
[2025-06-17 15:09:38,740] WARNING - __main__ - main.py:8308 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:38,740] WARNING - __main__ - main.py:8309 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:38,740] WARNING - __main__ - main.py:8310 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔄   - Python calculated phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:38,741] INFO - __main__ - main.py:8318 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:09:38,741] INFO - __main__ - main.py:8319 -   - Total execution time: 1.536s
[2025-06-17 15:09:38,741] WARNING - __main__ - main.py:5643 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:09:38,741] WARNING - __main__ - main.py:5644 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖   - Content length: 362 chars
[2025-06-17 15:09:38,742] WARNING - __main__ - main.py:5645 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'current_probing_level_number': 5, 'current_question_index': 2, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:09:38,742] WARNING - __main__ - main.py:5646 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🤖   - Raw state block: None...
[2025-06-17 15:09:38,742] INFO - __main__ - main.py:5662 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:09:38,742] INFO - __main__ - main.py:5663 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q2_ask_q3, Session=diagnostic_probing_L5_eval_q1_ask_q2, Final=diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:39,049] INFO - __main__ - main.py:5772 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI processing completed in 1.85s
[2025-06-17 15:09:39,049] WARNING - __main__ - main.py:5783 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q1_ask_q2', new_phase='diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:39,050] INFO - __main__ - main.py:3897 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI state update validation passed: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:39,050] WARNING - __main__ - main.py:5792 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:09:39,051] WARNING - __main__ - main.py:5797 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔄 PHASE TRANSITION: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:39,052] WARNING - __main__ - main.py:5806 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:09:39,053] WARNING - __main__ - main.py:5807 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:39,053] WARNING - __main__ - main.py:5808 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:09:39,054] WARNING - __main__ - main.py:5809 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'current_probing_level_number': 5, 'current_question_index': 2, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:09:39,056] WARNING - __main__ - main.py:5810 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:39,056] WARNING - __main__ - main.py:5813 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:09:39,057] WARNING - __main__ - main.py:5814 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 15:09:39,057] WARNING - __main__ - main.py:5815 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q2_ask_q3', 'current_probing_level_number': 5, 'current_question_index': 2, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 15:09:39,058] WARNING - __main__ - main.py:5816 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:09:39,058] WARNING - __main__ - main.py:5817 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 💾   - Phase change: True
[2025-06-17 15:09:39,058] INFO - __main__ - main.py:3929 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:09:39,059] INFO - __main__ - main.py:3930 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 -> diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:39,059] INFO - __main__ - main.py:3931 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Current level: 5
[2025-06-17 15:09:39,060] INFO - __main__ - main.py:3932 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Question index: 2
[2025-06-17 15:09:39,060] INFO - __main__ - main.py:3933 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   First encounter: True
[2025-06-17 15:09:39,061] INFO - __main__ - main.py:3938 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Answers collected: 2
[2025-06-17 15:09:39,061] INFO - __main__ - main.py:3939 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Levels failed: 0
[2025-06-17 15:09:39,062] INFO - __main__ - main.py:3897 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI state update validation passed: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:39,062] INFO - __main__ - main.py:3943 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   State update valid: True
[2025-06-17 15:09:39,064] INFO - __main__ - main.py:3950 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db]   Diagnostic complete: False
[2025-06-17 15:09:39,065] WARNING - __main__ - main.py:5829 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 2, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:09:39,651] WARNING - __main__ - main.py:5856 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:09:39,651] WARNING - __main__ - main.py:5857 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:39,651] WARNING - __main__ - main.py:5858 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Probing Level: 5
[2025-06-17 15:09:39,652] WARNING - __main__ - main.py:5859 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Question Index: 2
[2025-06-17 15:09:39,652] WARNING - __main__ - main.py:5860 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Diagnostic Complete: False
[2025-06-17 15:09:40,527] INFO - __main__ - main.py:5919 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:09:40,528] WARNING - __main__ - main.py:5920 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:09:40,528] WARNING - __main__ - main.py:5921 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:09:40,529] WARNING - __main__ - main.py:5922 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:09:40,530] WARNING - __main__ - main.py:5923 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] ✅   - Interaction logged successfully
[2025-06-17 15:09:40,531] INFO - __main__ - main.py:9216 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:09:40,532] DEBUG - __main__ - main.py:2753 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:09:40,534] DEBUG - __main__ - main.py:5969 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] No final assessment data found in AI response
[2025-06-17 15:09:40,536] INFO - __main__ - main.py:6034 - [61652124-c0e1-4dd5-bb4f-7ed17fd907db] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:09:40,537] WARNING - __main__ - main.py:625 - High response time detected: 5.66s for enhance_content_api
[2025-06-17 15:09:40,538] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:09:40] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 15:10:10,203] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:10:10,203] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "f428a232-019c-4f47-b528-54aa60b3546b", "timestamp": "2025-06-17T14:10:10.203553+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixed shape. It doesn't squish or spread out like water, so it stays big and covers you. It's hard and strong, so you can't see through it! It blocks your view from the person who is \"it.\" It stays in one place, it doesn't flow away like water or float away like air!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your Basic Science and Technology tutor, and I'm excited to help you learn about States of Matter. We'll start with a short diagnostic assessment to understand your current knowledge. This helps me tailor the best learning experience for you. Are you ready to begin with some questions about Discover, Three, States, Matter, and Transformation?", "timestamp": "2025-06-17T14:08:05.816Z"}, {"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T14:08:14.157Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about States of Matter: Imagine you're playing with a water balloon. Describe how the water changes state if the balloon bursts on a hot sunny day in Nigeria. Think about what happens to the water and what state it's in before and after the balloon breaks.", "timestamp": "2025-06-17T14:08:20.246Z"}, {"role": "user", "content": "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!", "timestamp": "2025-06-17T14:09:01.546Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! You correctly identified that the water in the balloon, before it bursts, is a liquid. Now, let's think about what happens to the water *after* the balloon bursts. Does the state of the water change, or does it remain a liquid?", "timestamp": "2025-06-17T14:09:07.180Z"}, {"role": "user", "content": "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", "timestamp": "2025-06-17T14:09:34.755Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! It shows you understand that a change in state requires more than just a change in its container. Now let's explore another situation. Imagine you're playing a game of hide-and-seek. You hide behind a large, solid rock. What state of matter is the rock, and what are some properties of matter that make it a good hiding place?", "timestamp": "2025-06-17T14:09:40.555Z"}]}}
[2025-06-17 15:10:10,206] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [f428a232-019c-4f47-b528-54aa60b3546b] Using temporary auth bypass with mock token
[2025-06-17 15:10:10,207] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:10:10,212] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:10:10,216] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:10:10,217] INFO - __main__ - main.py:5180 - [enhance_content_api][f428a232-019c-4f47-b528-54aa60b3546b] Processing enhance content request
[2025-06-17 15:10:10,218] INFO - __main__ - main.py:5226 - [f428a232-019c-4f47-b528-54aa60b3546b][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it 
[2025-06-17 15:10:10,522] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:10:10,523] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:10:10,523] INFO - __main__ - main.py:5272 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:10:10,523] INFO - __main__ - main.py:5311 - [f428a232-019c-4f47-b528-54aa60b3546b] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:10:10,523] INFO - __main__ - main.py:2273 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:10:11,083] INFO - __main__ - main.py:2332 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:10:11,084] INFO - __main__ - main.py:2332 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:10:11,084] INFO - __main__ - main.py:2332 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:10:11,084] INFO - __main__ - main.py:2332 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:10:11,085] INFO - __main__ - main.py:2332 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:10:11,085] INFO - __main__ - main.py:2401 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:10:11,086] DEBUG - __main__ - main.py:2415 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:10:11,086] DEBUG - __main__ - main.py:2418 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:10:11,087] DEBUG - __main__ - main.py:2419 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:10:11,088] DEBUG - __main__ - main.py:2420 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:10:11,089] INFO - __main__ - main.py:2424 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:10:11,597] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:10:11,597] INFO - __main__ - main.py:2434 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Gemini API call completed in 0.51s. Raw response: 'materials'
[2025-06-17 15:10:11,598] DEBUG - __main__ - main.py:2456 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:10:11,598] INFO - __main__ - main.py:2461 - [f428a232-019c-4f47-b528-54aa60b3546b] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:10:11,599] INFO - __main__ - main.py:5344 - [f428a232-019c-4f47-b528-54aa60b3546b] Successfully inferred module ID via AI: materials
[2025-06-17 15:10:11,600] INFO - __main__ - main.py:5370 - [f428a232-019c-4f47-b528-54aa60b3546b] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:10:11,883] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:10:12,461] WARNING - __main__ - main.py:5391 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:10:12,461] WARNING - __main__ - main.py:5392 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Session exists: True
[2025-06-17 15:10:12,461] WARNING - __main__ - main.py:5393 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Current phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,462] WARNING - __main__ - main.py:5394 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'lesson_context_snapshot', 'student_name', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 15:10:12,464] INFO - __main__ - main.py:5458 - [f428a232-019c-4f47-b528-54aa60b3546b] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:10:12,466] INFO - __main__ - main.py:5459 - [f428a232-019c-4f47-b528-54aa60b3546b]   assigned_level_for_teaching (session): None
[2025-06-17 15:10:12,467] INFO - __main__ - main.py:5460 - [f428a232-019c-4f47-b528-54aa60b3546b]   latest_assessed_level (profile): None
[2025-06-17 15:10:12,468] INFO - __main__ - main.py:5461 - [f428a232-019c-4f47-b528-54aa60b3546b]   teaching_level_for_returning_student: None
[2025-06-17 15:10:12,469] INFO - __main__ - main.py:5462 - [f428a232-019c-4f47-b528-54aa60b3546b]   has_completed_diagnostic_before: False
[2025-06-17 15:10:12,470] INFO - __main__ - main.py:5463 - [f428a232-019c-4f47-b528-54aa60b3546b]   is_first_encounter_for_module: True
[2025-06-17 15:10:12,470] WARNING - __main__ - main.py:5468 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:10:12,470] INFO - __main__ - main.py:5474 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:10:12,470] INFO - __main__ - main.py:5475 - [f428a232-019c-4f47-b528-54aa60b3546b]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,471] INFO - __main__ - main.py:5476 - [f428a232-019c-4f47-b528-54aa60b3546b]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:10:12,471] INFO - __main__ - main.py:5477 - [f428a232-019c-4f47-b528-54aa60b3546b]   Is first encounter: True
[2025-06-17 15:10:12,471] INFO - __main__ - main.py:5478 - [f428a232-019c-4f47-b528-54aa60b3546b]   Diagnostic completed: False
[2025-06-17 15:10:12,471] INFO - __main__ - main.py:5484 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,472] INFO - __main__ - main.py:5498 - [f428a232-019c-4f47-b528-54aa60b3546b] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:10:12,472] INFO - __main__ - main.py:5500 - [f428a232-019c-4f47-b528-54aa60b3546b] Final phase for AI logic: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,472] INFO - __main__ - main.py:5514 - [f428a232-019c-4f47-b528-54aa60b3546b] NEW SESSION: Using phase-based question index 1 from phase diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,473] INFO - __main__ - main.py:3692 - [f428a232-019c-4f47-b528-54aa60b3546b] Diagnostic context validation passed
[2025-06-17 15:10:12,473] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,473] WARNING - __main__ - main.py:5586 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q2_ask_q3' for first encounter
[2025-06-17 15:10:12,474] INFO - __main__ - main.py:3802 - [f428a232-019c-4f47-b528-54aa60b3546b] Enhanced diagnostic context with 35 fields
[2025-06-17 15:10:12,474] INFO - __main__ - main.py:5608 - [f428a232-019c-4f47-b528-54aa60b3546b] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,474] DEBUG - __main__ - main.py:5609 - [f428a232-019c-4f47-b528-54aa60b3546b] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:10:12,474] WARNING - __main__ - main.py:5617 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:10:12,475] WARNING - __main__ - main.py:5618 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:10:12,475] WARNING - __main__ - main.py:5619 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖   - Student query: That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixe...
[2025-06-17 15:10:12,475] WARNING - __main__ - main.py:5620 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:10:12,476] INFO - __main__ - main.py:6493 - [f428a232-019c-4f47-b528-54aa60b3546b] enhance_lesson_content invoked. Query: 'That's a fun game!  The big rock is a solid!  It's...'
[2025-06-17 15:10:12,476] INFO - __main__ - main.py:6536 - [f428a232-019c-4f47-b528-54aa60b3546b] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q2_ask_q3', processed = 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,476] INFO - __main__ - main.py:6547 - [f428a232-019c-4f47-b528-54aa60b3546b][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q2_ask_q3', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:10:12,477] INFO - __main__ - main.py:6577 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC ANSWER: Storing Q2 answer from phase diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,477] INFO - __main__ - main.py:6586 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC ANSWER STORED: q2 = 'That's a fun game!  The big rock is a solid!  It's...'
[2025-06-17 15:10:12,477] INFO - __main__ - main.py:6587 - [f428a232-019c-4f47-b528-54aa60b3546b] Total diagnostic answers now: 2/5
[2025-06-17 15:10:12,477] INFO - __main__ - main.py:6628 - [f428a232-019c-4f47-b528-54aa60b3546b] 📝 DIAGNOSTIC PROGRESSION: Continue with 2/5 answers, phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,478] INFO - __main__ - main.py:6766 - [f428a232-019c-4f47-b528-54aa60b3546b][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,478] INFO - __main__ - main.py:6773 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,479] INFO - __main__ - main.py:6774 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] Questions asked: 2/5
[2025-06-17 15:10:12,480] INFO - __main__ - main.py:6775 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] Question index: 1
[2025-06-17 15:10:12,481] INFO - __main__ - main.py:6776 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] Student answers count: 2
[2025-06-17 15:10:12,482] INFO - __main__ - main.py:6781 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] Answer q1: That's a good question!  Right after the water bal...
[2025-06-17 15:10:12,483] INFO - __main__ - main.py:6781 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] Answer q2: That's a fun game!  The big rock is a solid!  It's...
[2025-06-17 15:10:12,483] INFO - __main__ - main.py:6805 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q2_ask_q3, Answers=2/5
[2025-06-17 15:10:12,484] INFO - __main__ - main.py:6808 - [f428a232-019c-4f47-b528-54aa60b3546b][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:10:12,484] INFO - __main__ - main.py:6839 - [f428a232-019c-4f47-b528-54aa60b3546b] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:10:12,484] INFO - __main__ - main.py:6853 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,485] INFO - __main__ - main.py:6861 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,485] INFO - __main__ - main.py:6906 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 DIAGNOSTIC EVAL DEBUG: Q3 response analysis:
[2025-06-17 15:10:12,486] INFO - __main__ - main.py:6907 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 DIAGNOSTIC EVAL DEBUG:   - user_query: 'That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixed shape. It doesn't squish or spread out like water, so it stays big and covers you. It's hard and strong, so you can't see through it! It blocks your view from the person who is "it." It stays in one place, it doesn't flow away like water or float away like air!'
[2025-06-17 15:10:12,486] INFO - __main__ - main.py:6908 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 DIAGNOSTIC EVAL DEBUG:   - Final result: True
[2025-06-17 15:10:12,487] INFO - __main__ - main.py:6922 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC PROGRESSION: Q3 answered → Q4 (diagnostic_probing_L5_eval_q3_ask_q4)
[2025-06-17 15:10:12,487] WARNING - __main__ - main.py:6933 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧 DIAGNOSTIC INDEX UPDATE: Updated current_q_index_for_prompt to 3 and context to 3
[2025-06-17 15:10:12,487] INFO - __main__ - main.py:7140 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4 (interaction 1)
[2025-06-17 15:10:12,488] WARNING - __main__ - main.py:7143 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:10:12,488] WARNING - __main__ - main.py:7144 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,488] WARNING - __main__ - main.py:7145 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - User query: 'That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixed shape. It doesn't squish or spread out like water, so it stays big and covers you. It's hard and strong, so you can't see through it! It blocks your view from the person who is "it." It stays in one place, it doesn't flow away like water or float away like air!'
[2025-06-17 15:10:12,489] WARNING - __main__ - main.py:7146 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Is actual student response: True
[2025-06-17 15:10:12,489] WARNING - __main__ - main.py:7147 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Current probing level: 5
[2025-06-17 15:10:12,490] WARNING - __main__ - main.py:7148 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Current question index: 3
[2025-06-17 15:10:12,490] WARNING - __main__ - main.py:7149 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Student answers count: 2
[2025-06-17 15:10:12,490] INFO - __main__ - main.py:7152 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:10:12,491] INFO - __main__ - main.py:7153 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,491] INFO - __main__ - main.py:7154 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:12,492] INFO - __main__ - main.py:7155 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧   - user_query: 'That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixed shape. It doesn't squish or spread out like water, so it stays big and covers you. It's hard and strong, so you can't see through it! It blocks your view from the person who is "it." It stays in one place, it doesn't flow away like water or float away like air!'
[2025-06-17 15:10:12,492] INFO - __main__ - main.py:7156 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:10:12,493] WARNING - __main__ - main.py:7159 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:10:12,493] WARNING - __main__ - main.py:7160 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,494] WARNING - __main__ - main.py:7161 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯   - Calculated new phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:12,494] WARNING - __main__ - main.py:7162 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯   - Phase changed: True
[2025-06-17 15:10:12,494] WARNING - __main__ - main.py:7165 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:10:12,495] WARNING - __main__ - main.py:7166 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯   - In diagnostic phase: True
[2025-06-17 15:10:12,495] WARNING - __main__ - main.py:7167 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯   - Student answers stored: {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!'}
[2025-06-17 15:10:12,499] WARNING - __main__ - main.py:7168 - [f428a232-019c-4f47-b528-54aa60b3546b] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:10:12,500] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:10:12,500] INFO - __main__ - main.py:7229 - [f428a232-019c-4f47-b528-54aa60b3546b][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_eval_q3_ask_q4', q_index='3', total_q_asked='3'
[2025-06-17 15:10:12,500] INFO - __main__ - main.py:7235 - [f428a232-019c-4f47-b528-54aa60b3546b][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:10:12,501] WARNING - __main__ - main.py:7260 - [f428a232-019c-4f47-b528-54aa60b3546b] 🚀 DIAGNOSTIC PHASE OVERRIDE: AI will receive 'diagnostic_probing_L5_eval_q3_ask_q4' instead of 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:12,501] WARNING - __main__ - main.py:7261 - [f428a232-019c-4f47-b528-54aa60b3546b] 🚀 REASON: Student responded to diagnostic_start_probe, need to progress to Q1
[2025-06-17 15:10:12,502] INFO - __main__ - main.py:7340 - [f428a232-019c-4f47-b528-54aa60b3546b] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:10:12,502] INFO - __main__ - main.py:7351 - [f428a232-019c-4f47-b528-54aa60b3546b] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:10:12,502] INFO - __main__ - main.py:7357 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Template placeholder successfully substituted
[2025-06-17 15:10:12,503] INFO - __main__ - main.py:7361 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Template contains calculated phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:12,503] INFO - __main__ - main.py:7405 - [f428a232-019c-4f47-b528-54aa60b3546b] Prompt truncated from 14583 to 10642 chars for performance
[2025-06-17 15:10:12,503] INFO - __main__ - main.py:7408 - [f428a232-019c-4f47-b528-54aa60b3546b] CONTENT QUALITY DEBUG:
[2025-06-17 15:10:12,504] INFO - __main__ - main.py:7409 -   - Final prompt length: 10642 characters
[2025-06-17 15:10:12,504] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:10:12,504] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:10:12,504] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:10:12,505] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:10:12,505] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_probing_L5_eval_q2_ask_q3
[2025-06-17 15:10:12,505] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:10:12,505] DEBUG - __main__ - main.py:7416 - [f428a232-019c-4f47-b528-54aa60b3546b] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_eval_q3_ask_q4", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_eval_q3_ask_q4
• You MUST use this exact ...
[2025-06-17 15:10:12,506] INFO - __main__ - main.py:7444 - [f428a232-019c-4f47-b528-54aa60b3546b] Gemini API call attempt 1/3
[2025-06-17 15:10:13,971] INFO - __main__ - main.py:7462 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:10:13,972] INFO - __main__ - main.py:7481 - [f428a232-019c-4f47-b528-54aa60b3546b] Gemini API call completed in 1.47s
[2025-06-17 15:10:13,972] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 463 characters
[2025-06-17 15:10:13,973] INFO - __main__ - main.py:7704 - [f428a232-019c-4f47-b528-54aa60b3546b][enhance_lesson_content] AI response: That's a great example about how a solid, like a big rock, keeps its shape! Now let's explore another scenario. Imagine you're playing hide-and-seek near a river.  If you were to hide behind a large p...
[2025-06-17 15:10:13,974] INFO - __main__ - main.py:7732 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:10:13,975] INFO - __main__ - main.py:7733 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 STATE DEBUG: AI response length: 463 chars
[2025-06-17 15:10:13,976] INFO - __main__ - main.py:7745 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_eval_q3_ask_q4", "interaction_count": 1}
[2025-06-17 15:10:13,977] INFO - __main__ - main.py:7747 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'interaction_count': 1}
[2025-06-17 15:10:13,978] INFO - __main__ - main.py:7811 - [f428a232-019c-4f47-b528-54aa60b3546b] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:10:13,979] INFO - __main__ - main.py:7817 - [f428a232-019c-4f47-b528-54aa60b3546b] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:10:13,980] INFO - __main__ - main.py:7837 - [f428a232-019c-4f47-b528-54aa60b3546b] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:10:13,981] INFO - __main__ - main.py:7841 - [f428a232-019c-4f47-b528-54aa60b3546b] Auto-corrected: added missing current_question_index
[2025-06-17 15:10:13,983] INFO - __main__ - main.py:7867 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 3}
[2025-06-17 15:10:13,984] INFO - __main__ - main.py:7879 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:10:13,984] INFO - __main__ - main.py:7922 - [f428a232-019c-4f47-b528-54aa60b3546b] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:10:13,985] INFO - __main__ - main.py:7931 - [f428a232-019c-4f47-b528-54aa60b3546b] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 3}
[2025-06-17 15:10:13,985] INFO - __main__ - main.py:7989 - [f428a232-019c-4f47-b528-54aa60b3546b] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=2/5
[2025-06-17 15:10:13,985] INFO - __main__ - main.py:7992 - [f428a232-019c-4f47-b528-54aa60b3546b] 📝 DIAGNOSTIC PROGRESSION: Need 3 more answers
[2025-06-17 15:10:13,986] INFO - __main__ - main.py:7994 - [f428a232-019c-4f47-b528-54aa60b3546b] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:10:13,986] INFO - __main__ - main.py:7998 - [f428a232-019c-4f47-b528-54aa60b3546b] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:10:13,986] INFO - __main__ - main.py:8030 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4 - trusting AI
[2025-06-17 15:10:13,987] INFO - __main__ - main.py:8075 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:13,987] WARNING - __main__ - main.py:8113 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 15:10:13,987] WARNING - __main__ - main.py:8114 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Phase: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:13,988] WARNING - __main__ - main.py:8115 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Question Index: 3
[2025-06-17 15:10:13,988] WARNING - __main__ - main.py:8116 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   - Probing Level: 5
[2025-06-17 15:10:13,988] INFO - __main__ - main.py:8120 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 15:10:13,989] INFO - __main__ - main.py:8121 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_eval_q3_ask_q4, Enforced phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:13,989] INFO - __main__ - main.py:8141 - [f428a232-019c-4f47-b528-54aa60b3546b] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:10:13,989] INFO - __main__ - main.py:8147 - [f428a232-019c-4f47-b528-54aa60b3546b] Phase transition: diagnostic_probing_L5_eval_q2_ask_q3 -> diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:13,990] INFO - __main__ - main.py:3972 - [f428a232-019c-4f47-b528-54aa60b3546b] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:13,990] INFO - __main__ - main.py:4074 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:13,990] INFO - __main__ - main.py:8223 - [f428a232-019c-4f47-b528-54aa60b3546b] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'current_probing_level_number': 5, 'current_question_index': 3, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!'}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:13,991] INFO - __main__ - main.py:8259 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:10:13,991] INFO - __main__ - main.py:8270 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧 FORCED personalization injection
[2025-06-17 15:10:13,992] INFO - __main__ - main.py:8281 - [f428a232-019c-4f47-b528-54aa60b3546b] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'current_probing_level_number': 5, 'current_question_index': 3, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!'}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:13,992] INFO - __main__ - main.py:8284 - [f428a232-019c-4f47-b528-54aa60b3546b] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:10:13,993] WARNING - __main__ - main.py:8305 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:10:13,993] WARNING - __main__ - main.py:8306 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔄   - AI provided state update: True
[2025-06-17 15:10:13,993] WARNING - __main__ - main.py:8308 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:13,994] WARNING - __main__ - main.py:8309 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:13,994] WARNING - __main__ - main.py:8310 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔄   - Python calculated phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:13,995] INFO - __main__ - main.py:8318 - [f428a232-019c-4f47-b528-54aa60b3546b] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:10:13,995] INFO - __main__ - main.py:8319 -   - Total execution time: 1.519s
[2025-06-17 15:10:13,997] WARNING - __main__ - main.py:5643 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:10:13,999] WARNING - __main__ - main.py:5644 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖   - Content length: 333 chars
[2025-06-17 15:10:13,999] WARNING - __main__ - main.py:5645 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'current_probing_level_number': 5, 'current_question_index': 3, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!'}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:14,000] WARNING - __main__ - main.py:5646 - [f428a232-019c-4f47-b528-54aa60b3546b] 🤖   - Raw state block: None...
[2025-06-17 15:10:14,001] INFO - __main__ - main.py:5662 - [f428a232-019c-4f47-b528-54aa60b3546b] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:10:14,001] INFO - __main__ - main.py:5663 - [f428a232-019c-4f47-b528-54aa60b3546b] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q3_ask_q4, Session=diagnostic_probing_L5_eval_q2_ask_q3, Final=diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:14,299] INFO - __main__ - main.py:5772 - [f428a232-019c-4f47-b528-54aa60b3546b] AI processing completed in 1.82s
[2025-06-17 15:10:14,300] WARNING - __main__ - main.py:5783 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q2_ask_q3', new_phase='diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:14,300] INFO - __main__ - main.py:3897 - [f428a232-019c-4f47-b528-54aa60b3546b] AI state update validation passed: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:14,301] WARNING - __main__ - main.py:5792 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:10:14,302] WARNING - __main__ - main.py:5797 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔄 PHASE TRANSITION: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:14,303] WARNING - __main__ - main.py:5806 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:10:14,303] WARNING - __main__ - main.py:5807 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:14,304] WARNING - __main__ - main.py:5808 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:10:14,305] WARNING - __main__ - main.py:5809 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'current_probing_level_number': 5, 'current_question_index': 3, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!'}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:14,306] WARNING - __main__ - main.py:5810 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:14,306] WARNING - __main__ - main.py:5813 - [f428a232-019c-4f47-b528-54aa60b3546b] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:10:14,307] WARNING - __main__ - main.py:5814 - [f428a232-019c-4f47-b528-54aa60b3546b] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q2_ask_q3'
[2025-06-17 15:10:14,307] WARNING - __main__ - main.py:5815 - [f428a232-019c-4f47-b528-54aa60b3546b] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q3_ask_q4', 'current_probing_level_number': 5, 'current_question_index': 3, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!'}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:14,307] WARNING - __main__ - main.py:5816 - [f428a232-019c-4f47-b528-54aa60b3546b] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:14,308] WARNING - __main__ - main.py:5817 - [f428a232-019c-4f47-b528-54aa60b3546b] 💾   - Phase change: True
[2025-06-17 15:10:14,308] INFO - __main__ - main.py:3929 - [f428a232-019c-4f47-b528-54aa60b3546b] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:10:14,308] INFO - __main__ - main.py:3930 - [f428a232-019c-4f47-b528-54aa60b3546b]   Phase transition: diagnostic_probing_L5_eval_q2_ask_q3 -> diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:14,309] INFO - __main__ - main.py:3931 - [f428a232-019c-4f47-b528-54aa60b3546b]   Current level: 5
[2025-06-17 15:10:14,309] INFO - __main__ - main.py:3932 - [f428a232-019c-4f47-b528-54aa60b3546b]   Question index: 3
[2025-06-17 15:10:14,309] INFO - __main__ - main.py:3933 - [f428a232-019c-4f47-b528-54aa60b3546b]   First encounter: True
[2025-06-17 15:10:14,310] INFO - __main__ - main.py:3938 - [f428a232-019c-4f47-b528-54aa60b3546b]   Answers collected: 2
[2025-06-17 15:10:14,310] INFO - __main__ - main.py:3939 - [f428a232-019c-4f47-b528-54aa60b3546b]   Levels failed: 0
[2025-06-17 15:10:14,310] INFO - __main__ - main.py:3897 - [f428a232-019c-4f47-b528-54aa60b3546b] AI state update validation passed: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:14,311] INFO - __main__ - main.py:3943 - [f428a232-019c-4f47-b528-54aa60b3546b]   State update valid: True
[2025-06-17 15:10:14,311] INFO - __main__ - main.py:3950 - [f428a232-019c-4f47-b528-54aa60b3546b]   Diagnostic complete: False
[2025-06-17 15:10:14,311] WARNING - __main__ - main.py:5829 - [f428a232-019c-4f47-b528-54aa60b3546b] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 3, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:10:14,883] WARNING - __main__ - main.py:5856 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:10:14,884] WARNING - __main__ - main.py:5857 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:14,885] WARNING - __main__ - main.py:5858 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Probing Level: 5
[2025-06-17 15:10:14,885] WARNING - __main__ - main.py:5859 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Question Index: 3
[2025-06-17 15:10:14,886] WARNING - __main__ - main.py:5860 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Diagnostic Complete: False
[2025-06-17 15:10:15,771] INFO - __main__ - main.py:5919 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:10:15,771] WARNING - __main__ - main.py:5920 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:10:15,772] WARNING - __main__ - main.py:5921 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:10:15,773] WARNING - __main__ - main.py:5922 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Phase transition: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:15,773] WARNING - __main__ - main.py:5923 - [f428a232-019c-4f47-b528-54aa60b3546b] ✅   - Interaction logged successfully
[2025-06-17 15:10:15,774] INFO - __main__ - main.py:9216 - [f428a232-019c-4f47-b528-54aa60b3546b] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:10:15,774] DEBUG - __main__ - main.py:2753 - [f428a232-019c-4f47-b528-54aa60b3546b] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:10:15,775] DEBUG - __main__ - main.py:5969 - [f428a232-019c-4f47-b528-54aa60b3546b] No final assessment data found in AI response
[2025-06-17 15:10:15,778] INFO - __main__ - main.py:6034 - [f428a232-019c-4f47-b528-54aa60b3546b] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:10:15,779] WARNING - __main__ - main.py:625 - High response time detected: 5.57s for enhance_content_api
[2025-06-17 15:10:15,782] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:10:15] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 15:10:44,988] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:10:44,988] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "d7eaea96-b0e4-4c52-b0bd-49669696268a", "timestamp": "2025-06-17T14:10:44.988659+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": [{"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T14:08:14.157Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about States of Matter: Imagine you're playing with a water balloon. Describe how the water changes state if the balloon bursts on a hot sunny day in Nigeria. Think about what happens to the water and what state it's in before and after the balloon breaks.", "timestamp": "2025-06-17T14:08:20.246Z"}, {"role": "user", "content": "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!", "timestamp": "2025-06-17T14:09:01.546Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! You correctly identified that the water in the balloon, before it bursts, is a liquid. Now, let's think about what happens to the water *after* the balloon bursts. Does the state of the water change, or does it remain a liquid?", "timestamp": "2025-06-17T14:09:07.180Z"}, {"role": "user", "content": "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", "timestamp": "2025-06-17T14:09:34.755Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! It shows you understand that a change in state requires more than just a change in its container. Now let's explore another situation. Imagine you're playing a game of hide-and-seek. You hide behind a large, solid rock. What state of matter is the rock, and what are some properties of matter that make it a good hiding place?", "timestamp": "2025-06-17T14:09:40.555Z"}, {"role": "user", "content": "That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixed shape. It doesn't squish or spread out like water, so it stays big and covers you. It's hard and strong, so you can't see through it! It blocks your view from the person who is \"it.\" It stays in one place, it doesn't flow away like water or float away like air!", "timestamp": "2025-06-17T14:10:10.079Z"}, {"role": "assistant", "content": "Hi Andrea! That's a great example about how a solid, like a big rock, keeps its shape! Now let's explore another scenario. Imagine you're playing hide-and-seek near a river. If you were to hide behind a large pile of sand, would that be as good a hiding place as the rock? Why or why not, considering the state of matter of the sand?", "timestamp": "2025-06-17T14:10:15.799Z"}]}}
[2025-06-17 15:10:44,990] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Using temporary auth bypass with mock token
[2025-06-17 15:10:44,991] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:10:44,998] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:10:45,001] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:10:45,001] INFO - __main__ - main.py:5180 - [enhance_content_api][d7eaea96-b0e4-4c52-b0bd-49669696268a] Processing enhance content request
[2025-06-17 15:10:45,003] INFO - __main__ - main.py:5226 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a s
[2025-06-17 15:10:45,303] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:10:45,304] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:10:45,304] INFO - __main__ - main.py:5272 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:10:45,305] INFO - __main__ - main.py:5311 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:10:45,305] INFO - __main__ - main.py:2273 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:10:45,867] INFO - __main__ - main.py:2332 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:10:45,868] INFO - __main__ - main.py:2332 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:10:45,868] INFO - __main__ - main.py:2332 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:10:45,869] INFO - __main__ - main.py:2332 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:10:45,869] INFO - __main__ - main.py:2332 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:10:45,870] INFO - __main__ - main.py:2401 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:10:45,871] DEBUG - __main__ - main.py:2415 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:10:45,871] DEBUG - __main__ - main.py:2418 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:10:45,873] DEBUG - __main__ - main.py:2419 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:10:45,874] DEBUG - __main__ - main.py:2420 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:10:45,875] INFO - __main__ - main.py:2424 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:10:46,455] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:10:46,455] INFO - __main__ - main.py:2434 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Gemini API call completed in 0.58s. Raw response: 'materials'
[2025-06-17 15:10:46,456] DEBUG - __main__ - main.py:2456 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:10:46,456] INFO - __main__ - main.py:2461 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:10:46,457] INFO - __main__ - main.py:5344 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Successfully inferred module ID via AI: materials
[2025-06-17 15:10:46,458] INFO - __main__ - main.py:5370 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:10:46,765] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:10:47,338] WARNING - __main__ - main.py:5391 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:10:47,340] WARNING - __main__ - main.py:5392 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Session exists: True
[2025-06-17 15:10:47,341] WARNING - __main__ - main.py:5393 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Current phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,342] WARNING - __main__ - main.py:5394 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'lesson_context_snapshot', 'student_name', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 15:10:47,345] INFO - __main__ - main.py:5458 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:10:47,346] INFO - __main__ - main.py:5459 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   assigned_level_for_teaching (session): None
[2025-06-17 15:10:47,348] INFO - __main__ - main.py:5460 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   latest_assessed_level (profile): None
[2025-06-17 15:10:47,349] INFO - __main__ - main.py:5461 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   teaching_level_for_returning_student: None
[2025-06-17 15:10:47,350] INFO - __main__ - main.py:5462 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   has_completed_diagnostic_before: False
[2025-06-17 15:10:47,351] INFO - __main__ - main.py:5463 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   is_first_encounter_for_module: True
[2025-06-17 15:10:47,351] WARNING - __main__ - main.py:5468 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:10:47,352] INFO - __main__ - main.py:5474 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:10:47,352] INFO - __main__ - main.py:5475 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,353] INFO - __main__ - main.py:5476 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:10:47,353] INFO - __main__ - main.py:5477 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Is first encounter: True
[2025-06-17 15:10:47,354] INFO - __main__ - main.py:5478 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Diagnostic completed: False
[2025-06-17 15:10:47,354] INFO - __main__ - main.py:5484 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,355] INFO - __main__ - main.py:5498 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:10:47,355] INFO - __main__ - main.py:5500 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Final phase for AI logic: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,356] INFO - __main__ - main.py:5514 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] NEW SESSION: Using phase-based question index 2 from phase diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,356] INFO - __main__ - main.py:3692 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Diagnostic context validation passed
[2025-06-17 15:10:47,357] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,357] WARNING - __main__ - main.py:5586 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q3_ask_q4' for first encounter
[2025-06-17 15:10:47,358] INFO - __main__ - main.py:3802 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Enhanced diagnostic context with 35 fields
[2025-06-17 15:10:47,358] INFO - __main__ - main.py:5608 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,358] DEBUG - __main__ - main.py:5609 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:10:47,359] WARNING - __main__ - main.py:5617 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:10:47,360] WARNING - __main__ - main.py:5618 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:10:47,360] WARNING - __main__ - main.py:5619 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖   - Student query: That's a clever question!  A large pile of sand would probably not be as good a hiding place as the ...
[2025-06-17 15:10:47,361] WARNING - __main__ - main.py:5620 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:10:47,362] INFO - __main__ - main.py:6493 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] enhance_lesson_content invoked. Query: 'That's a clever question!  A large pile of sand wo...'
[2025-06-17 15:10:47,362] INFO - __main__ - main.py:6536 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q3_ask_q4', processed = 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,363] INFO - __main__ - main.py:6547 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q3_ask_q4', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:10:47,365] INFO - __main__ - main.py:6577 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC ANSWER: Storing Q3 answer from phase diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,365] INFO - __main__ - main.py:6586 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC ANSWER STORED: q3 = 'That's a clever question!  A large pile of sand wo...'
[2025-06-17 15:10:47,366] INFO - __main__ - main.py:6587 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Total diagnostic answers now: 3/5
[2025-06-17 15:10:47,366] INFO - __main__ - main.py:6628 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 📝 DIAGNOSTIC PROGRESSION: Continue with 3/5 answers, phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,366] INFO - __main__ - main.py:6766 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,367] INFO - __main__ - main.py:6773 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,367] INFO - __main__ - main.py:6774 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Questions asked: 3/5
[2025-06-17 15:10:47,367] INFO - __main__ - main.py:6775 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Question index: 2
[2025-06-17 15:10:47,368] INFO - __main__ - main.py:6776 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Student answers count: 3
[2025-06-17 15:10:47,368] INFO - __main__ - main.py:6781 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Answer q1: That's a good question!  Right after the water bal...
[2025-06-17 15:10:47,368] INFO - __main__ - main.py:6781 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Answer q2: That's a fun game!  The big rock is a solid!  It's...
[2025-06-17 15:10:47,368] INFO - __main__ - main.py:6781 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] Answer q3: That's a clever question!  A large pile of sand wo...
[2025-06-17 15:10:47,369] INFO - __main__ - main.py:6805 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q3_ask_q4, Answers=3/5
[2025-06-17 15:10:47,369] INFO - __main__ - main.py:6808 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:10:47,370] INFO - __main__ - main.py:6839 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:10:47,370] INFO - __main__ - main.py:6853 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,370] INFO - __main__ - main.py:6861 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,371] INFO - __main__ - main.py:6906 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 DIAGNOSTIC EVAL DEBUG: Q4 response analysis:
[2025-06-17 15:10:47,371] INFO - __main__ - main.py:6907 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 DIAGNOSTIC EVAL DEBUG:   - user_query: 'That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.'
[2025-06-17 15:10:47,371] INFO - __main__ - main.py:6908 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 DIAGNOSTIC EVAL DEBUG:   - Final result: True
[2025-06-17 15:10:47,449] INFO - __main__ - main.py:6938 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC PROGRESSION: Q4 answered → Q5 Final Question (diagnostic_probing_L5_eval_q4_ask_q5)
[2025-06-17 15:10:47,449] WARNING - __main__ - main.py:6949 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧 DIAGNOSTIC INDEX UPDATE Q5: Updated current_q_index_for_prompt to 4 and context to 4
[2025-06-17 15:10:47,450] INFO - __main__ - main.py:7140 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5 (interaction 1)
[2025-06-17 15:10:47,451] WARNING - __main__ - main.py:7143 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:10:47,452] WARNING - __main__ - main.py:7144 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,453] WARNING - __main__ - main.py:7145 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - User query: 'That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.'
[2025-06-17 15:10:47,477] WARNING - __main__ - main.py:7146 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Is actual student response: True
[2025-06-17 15:10:47,478] WARNING - __main__ - main.py:7147 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Current probing level: 5
[2025-06-17 15:10:47,482] WARNING - __main__ - main.py:7148 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Current question index: 4
[2025-06-17 15:10:47,485] WARNING - __main__ - main.py:7149 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Student answers count: 3
[2025-06-17 15:10:47,487] INFO - __main__ - main.py:7152 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:10:47,488] INFO - __main__ - main.py:7153 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,488] INFO - __main__ - main.py:7154 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:47,489] INFO - __main__ - main.py:7155 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧   - user_query: 'That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.'
[2025-06-17 15:10:47,490] INFO - __main__ - main.py:7156 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:10:47,491] WARNING - __main__ - main.py:7159 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:10:47,491] WARNING - __main__ - main.py:7160 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,492] WARNING - __main__ - main.py:7161 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯   - Calculated new phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:47,493] WARNING - __main__ - main.py:7162 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯   - Phase changed: True
[2025-06-17 15:10:47,494] WARNING - __main__ - main.py:7165 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:10:47,495] WARNING - __main__ - main.py:7166 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯   - In diagnostic phase: True
[2025-06-17 15:10:47,496] WARNING - __main__ - main.py:7167 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯   - Student answers stored: {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around."}
[2025-06-17 15:10:47,498] WARNING - __main__ - main.py:7168 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:10:47,500] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:10:47,500] INFO - __main__ - main.py:7229 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_eval_q4_ask_q5', q_index='4', total_q_asked='4'
[2025-06-17 15:10:47,501] INFO - __main__ - main.py:7235 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:10:47,502] WARNING - __main__ - main.py:7260 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🚀 DIAGNOSTIC PHASE OVERRIDE: AI will receive 'diagnostic_probing_L5_eval_q4_ask_q5' instead of 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:47,502] WARNING - __main__ - main.py:7261 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🚀 REASON: Student responded to diagnostic_start_probe, need to progress to Q1
[2025-06-17 15:10:47,503] INFO - __main__ - main.py:7340 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:10:47,503] INFO - __main__ - main.py:7351 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:10:47,504] INFO - __main__ - main.py:7357 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Template placeholder successfully substituted
[2025-06-17 15:10:47,504] INFO - __main__ - main.py:7361 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Template contains calculated phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:47,505] INFO - __main__ - main.py:7405 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Prompt truncated from 15228 to 10642 chars for performance
[2025-06-17 15:10:47,506] INFO - __main__ - main.py:7408 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] CONTENT QUALITY DEBUG:
[2025-06-17 15:10:47,506] INFO - __main__ - main.py:7409 -   - Final prompt length: 10642 characters
[2025-06-17 15:10:47,507] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:10:47,508] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:10:47,508] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:10:47,509] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:10:47,510] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_probing_L5_eval_q3_ask_q4
[2025-06-17 15:10:47,511] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:10:47,511] DEBUG - __main__ - main.py:7416 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_eval_q4_ask_q5", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_eval_q4_ask_q5
• You MUST use this exact ...
[2025-06-17 15:10:47,512] INFO - __main__ - main.py:7444 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Gemini API call attempt 1/3
[2025-06-17 15:10:49,032] INFO - __main__ - main.py:7462 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:10:49,033] INFO - __main__ - main.py:7481 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Gemini API call completed in 1.52s
[2025-06-17 15:10:49,035] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 543 characters
[2025-06-17 15:10:49,036] INFO - __main__ - main.py:7704 - [d7eaea96-b0e4-4c52-b0bd-49669696268a][enhance_lesson_content] AI response: That's a great observation, Andrea!  You correctly identified that while sand is a solid, its loose nature makes it a poor hiding place compared to a solid rock.  Now, let's consider this: Imagine you...
[2025-06-17 15:10:49,037] INFO - __main__ - main.py:7732 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:10:49,038] INFO - __main__ - main.py:7733 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 STATE DEBUG: AI response length: 543 chars
[2025-06-17 15:10:49,039] INFO - __main__ - main.py:7745 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_eval_q4_ask_q5", "interaction_count": 1}
[2025-06-17 15:10:49,040] INFO - __main__ - main.py:7747 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'interaction_count': 1}
[2025-06-17 15:10:49,040] INFO - __main__ - main.py:7811 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:10:49,041] INFO - __main__ - main.py:7817 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:10:49,042] INFO - __main__ - main.py:7837 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:10:49,042] INFO - __main__ - main.py:7841 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Auto-corrected: added missing current_question_index
[2025-06-17 15:10:49,043] INFO - __main__ - main.py:7867 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 4}
[2025-06-17 15:10:49,043] INFO - __main__ - main.py:7879 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:10:49,044] INFO - __main__ - main.py:7922 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:10:49,044] INFO - __main__ - main.py:7931 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 4}
[2025-06-17 15:10:49,045] INFO - __main__ - main.py:7989 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=3/5
[2025-06-17 15:10:49,045] INFO - __main__ - main.py:7992 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 📝 DIAGNOSTIC PROGRESSION: Need 2 more answers
[2025-06-17 15:10:49,046] INFO - __main__ - main.py:7994 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:10:49,048] INFO - __main__ - main.py:7998 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:10:49,049] INFO - __main__ - main.py:8030 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5 - trusting AI
[2025-06-17 15:10:49,050] INFO - __main__ - main.py:8075 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,051] WARNING - __main__ - main.py:8113 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 15:10:49,051] WARNING - __main__ - main.py:8114 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Phase: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,052] WARNING - __main__ - main.py:8115 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Question Index: 4
[2025-06-17 15:10:49,052] WARNING - __main__ - main.py:8116 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   - Probing Level: 5
[2025-06-17 15:10:49,052] INFO - __main__ - main.py:8120 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 15:10:49,053] INFO - __main__ - main.py:8121 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_eval_q4_ask_q5, Enforced phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,053] INFO - __main__ - main.py:8141 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:10:49,054] INFO - __main__ - main.py:8147 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Phase transition: diagnostic_probing_L5_eval_q3_ask_q4 -> diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,054] INFO - __main__ - main.py:3972 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,055] INFO - __main__ - main.py:4074 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,055] INFO - __main__ - main.py:8223 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'current_probing_level_number': 5, 'current_question_index': 4, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around."}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:49,056] INFO - __main__ - main.py:8259 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:10:49,056] INFO - __main__ - main.py:8275 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ AI response already includes personalization
[2025-06-17 15:10:49,057] INFO - __main__ - main.py:8281 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'current_probing_level_number': 5, 'current_question_index': 4, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around."}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:49,057] INFO - __main__ - main.py:8284 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:10:49,057] WARNING - __main__ - main.py:8305 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:10:49,058] WARNING - __main__ - main.py:8306 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔄   - AI provided state update: True
[2025-06-17 15:10:49,058] WARNING - __main__ - main.py:8308 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:49,059] WARNING - __main__ - main.py:8309 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:49,059] WARNING - __main__ - main.py:8310 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔄   - Python calculated phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:49,059] INFO - __main__ - main.py:8318 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:10:49,060] INFO - __main__ - main.py:8319 -   - Total execution time: 1.698s
[2025-06-17 15:10:49,060] WARNING - __main__ - main.py:5643 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:10:49,060] WARNING - __main__ - main.py:5644 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖   - Content length: 401 chars
[2025-06-17 15:10:49,060] WARNING - __main__ - main.py:5645 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'current_probing_level_number': 5, 'current_question_index': 4, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around."}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:49,061] WARNING - __main__ - main.py:5646 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🤖   - Raw state block: None...
[2025-06-17 15:10:49,061] INFO - __main__ - main.py:5662 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:10:49,061] INFO - __main__ - main.py:5663 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q4_ask_q5, Session=diagnostic_probing_L5_eval_q3_ask_q4, Final=diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,383] INFO - __main__ - main.py:5772 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI processing completed in 2.02s
[2025-06-17 15:10:49,384] WARNING - __main__ - main.py:5783 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q3_ask_q4', new_phase='diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:49,384] INFO - __main__ - main.py:3897 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI state update validation passed: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,385] WARNING - __main__ - main.py:5792 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:10:49,385] WARNING - __main__ - main.py:5797 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔄 PHASE TRANSITION: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,386] WARNING - __main__ - main.py:5806 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:10:49,387] WARNING - __main__ - main.py:5807 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:49,387] WARNING - __main__ - main.py:5808 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:10:49,388] WARNING - __main__ - main.py:5809 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'current_probing_level_number': 5, 'current_question_index': 4, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around."}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:49,389] WARNING - __main__ - main.py:5810 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:49,390] WARNING - __main__ - main.py:5813 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:10:49,390] WARNING - __main__ - main.py:5814 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q3_ask_q4'
[2025-06-17 15:10:49,391] WARNING - __main__ - main.py:5815 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q4_ask_q5', 'current_probing_level_number': 5, 'current_question_index': 4, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around."}, 'levels_probed_and_failed': []}
[2025-06-17 15:10:49,391] WARNING - __main__ - main.py:5816 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:10:49,392] WARNING - __main__ - main.py:5817 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 💾   - Phase change: True
[2025-06-17 15:10:49,392] INFO - __main__ - main.py:3929 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:10:49,393] INFO - __main__ - main.py:3930 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Phase transition: diagnostic_probing_L5_eval_q3_ask_q4 -> diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,393] INFO - __main__ - main.py:3931 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Current level: 5
[2025-06-17 15:10:49,393] INFO - __main__ - main.py:3932 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Question index: 4
[2025-06-17 15:10:49,394] INFO - __main__ - main.py:3933 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   First encounter: True
[2025-06-17 15:10:49,394] INFO - __main__ - main.py:3938 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Answers collected: 3
[2025-06-17 15:10:49,394] INFO - __main__ - main.py:3939 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Levels failed: 0
[2025-06-17 15:10:49,395] INFO - __main__ - main.py:3897 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI state update validation passed: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,395] INFO - __main__ - main.py:3943 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   State update valid: True
[2025-06-17 15:10:49,395] INFO - __main__ - main.py:3950 - [d7eaea96-b0e4-4c52-b0bd-49669696268a]   Diagnostic complete: False
[2025-06-17 15:10:49,397] WARNING - __main__ - main.py:5829 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 4, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:10:49,944] WARNING - __main__ - main.py:5856 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:10:49,945] WARNING - __main__ - main.py:5857 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:49,945] WARNING - __main__ - main.py:5858 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Probing Level: 5
[2025-06-17 15:10:49,946] WARNING - __main__ - main.py:5859 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Question Index: 4
[2025-06-17 15:10:49,946] WARNING - __main__ - main.py:5860 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Diagnostic Complete: False
[2025-06-17 15:10:50,839] INFO - __main__ - main.py:5919 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:10:50,839] WARNING - __main__ - main.py:5920 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:10:50,840] WARNING - __main__ - main.py:5921 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:10:50,840] WARNING - __main__ - main.py:5922 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Phase transition: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:10:50,841] WARNING - __main__ - main.py:5923 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] ✅   - Interaction logged successfully
[2025-06-17 15:10:50,842] INFO - __main__ - main.py:9216 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:10:50,843] DEBUG - __main__ - main.py:2753 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:10:50,844] DEBUG - __main__ - main.py:5969 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] No final assessment data found in AI response
[2025-06-17 15:10:50,846] INFO - __main__ - main.py:6034 - [d7eaea96-b0e4-4c52-b0bd-49669696268a] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:10:50,849] WARNING - __main__ - main.py:625 - High response time detected: 5.86s for enhance_content_api
[2025-06-17 15:10:50,850] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:10:50] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 15:11:19,872] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 15:11:19,874] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "353ce001-8aed-46df-85b5-719d6b3e9bdb", "timestamp": "2025-06-17T14:11:19.873406+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-001", "content_to_enhance": "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80", "chat_history": [{"role": "user", "content": "Hi! This sounds like fun!  Before the balloon bursts, the water inside is a liquid. It's all squishy and wobbly in the balloon.  When the balloon bursts, the water is still a liquid as it splashes everywhere!  But if that water just stays on the hot ground in the sun (especially here in Nigeria where it's sunny!), it will start to disappear! It turns into an invisible gas called water vapor and goes up into the air because of the heat from the sun. That's called evaporating!", "timestamp": "2025-06-17T14:09:01.546Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! You correctly identified that the water in the balloon, before it bursts, is a liquid. Now, let's think about what happens to the water *after* the balloon bursts. Does the state of the water change, or does it remain a liquid?", "timestamp": "2025-06-17T14:09:07.180Z"}, {"role": "user", "content": "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", "timestamp": "2025-06-17T14:09:34.755Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! It shows you understand that a change in state requires more than just a change in its container. Now let's explore another situation. Imagine you're playing a game of hide-and-seek. You hide behind a large, solid rock. What state of matter is the rock, and what are some properties of matter that make it a good hiding place?", "timestamp": "2025-06-17T14:09:40.555Z"}, {"role": "user", "content": "That's a fun game!  The big rock is a solid!  It's a good hiding place because:  It has its own fixed shape. It doesn't squish or spread out like water, so it stays big and covers you. It's hard and strong, so you can't see through it! It blocks your view from the person who is \"it.\" It stays in one place, it doesn't flow away like water or float away like air!", "timestamp": "2025-06-17T14:10:10.079Z"}, {"role": "assistant", "content": "Hi Andrea! That's a great example about how a solid, like a big rock, keeps its shape! Now let's explore another scenario. Imagine you're playing hide-and-seek near a river. If you were to hide behind a large pile of sand, would that be as good a hiding place as the rock? Why or why not, considering the state of matter of the sand?", "timestamp": "2025-06-17T14:10:15.799Z"}, {"role": "user", "content": "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", "timestamp": "2025-06-17T14:10:44.855Z"}, {"role": "assistant", "content": "That's a great observation, Andrea! You correctly identified that while sand is a solid, its loose nature makes it a poor hiding place compared to a solid rock. Now, let's consider this: Imagine you have a glass of water, an ice cube, and some steam rising from a boiling pot. Can you explain how these three examples demonstrate the three states of matter and how they can change from one to another?", "timestamp": "2025-06-17T14:10:50.867Z"}]}}
[2025-06-17 15:11:19,875] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Using temporary auth bypass with mock token
[2025-06-17 15:11:19,875] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 15:11:19,878] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 15:11:19,881] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 15:11:19,882] INFO - __main__ - main.py:5180 - [enhance_content_api][353ce001-8aed-46df-85b5-719d6b3e9bdb] Processing enhance content request
[2025-06-17 15:11:19,883] INFO - __main__ - main.py:5226 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-BST-001', 'content_to_enhance': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny wa
[2025-06-17 15:11:20,931] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 15:11:20,931] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 15:11:20,932] INFO - __main__ - main.py:5272 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 15:11:20,932] INFO - __main__ - main.py:5311 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-17 15:11:20,933] INFO - __main__ - main.py:2273 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Inferring module for subject 'science', lesson 'States of Matter'.
[2025-06-17 15:11:22,203] INFO - __main__ - main.py:2332 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-17 15:11:22,204] INFO - __main__ - main.py:2332 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-17 15:11:22,205] INFO - __main__ - main.py:2332 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-17 15:11:22,206] INFO - __main__ - main.py:2332 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-17 15:11:22,206] INFO - __main__ - main.py:2332 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-17 15:11:22,207] INFO - __main__ - main.py:2401 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-17 15:11:22,208] DEBUG - __main__ - main.py:2415 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-17 15:11:22,209] DEBUG - __main__ - main.py:2418 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ...
[2025-06-17 15:11:22,210] DEBUG - __main__ - main.py:2419 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference Lesson Summary (first 300 chars): Lesson Title: States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Show. Introduction: In today's lesson, we will be ex...
[2025-06-17 15:11:22,211] DEBUG - __main__ - main.py:2420 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-17 15:11:22,212] INFO - __main__ - main.py:2424 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Calling Gemini API for module inference...
[2025-06-17 15:11:22,717] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-17 15:11:22,719] INFO - __main__ - main.py:2434 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Gemini API call completed in 0.50s. Raw response: 'materials'
[2025-06-17 15:11:22,720] DEBUG - __main__ - main.py:2456 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Cleaned slug: 'materials'
[2025-06-17 15:11:22,722] INFO - __main__ - main.py:2461 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-17 15:11:22,723] INFO - __main__ - main.py:5344 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Successfully inferred module ID via AI: materials
[2025-06-17 15:11:22,724] INFO - __main__ - main.py:5370 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-17 15:11:23,010] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-17 15:11:23,620] WARNING - __main__ - main.py:5391 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 SESSION STATE DEBUG:
[2025-06-17 15:11:23,622] WARNING - __main__ - main.py:5392 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Session exists: True
[2025-06-17 15:11:23,623] WARNING - __main__ - main.py:5393 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Current phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,624] WARNING - __main__ - main.py:5394 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'lesson_context_snapshot', 'student_name', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 15:11:23,626] INFO - __main__ - main.py:5458 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 15:11:23,627] INFO - __main__ - main.py:5459 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   assigned_level_for_teaching (session): None
[2025-06-17 15:11:23,627] INFO - __main__ - main.py:5460 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   latest_assessed_level (profile): None
[2025-06-17 15:11:23,628] INFO - __main__ - main.py:5461 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   teaching_level_for_returning_student: None
[2025-06-17 15:11:23,628] INFO - __main__ - main.py:5462 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   has_completed_diagnostic_before: False
[2025-06-17 15:11:23,630] INFO - __main__ - main.py:5463 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   is_first_encounter_for_module: True
[2025-06-17 15:11:23,634] WARNING - __main__ - main.py:5468 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 15:11:23,635] INFO - __main__ - main.py:5474 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 PHASE INVESTIGATION:
[2025-06-17 15:11:23,636] INFO - __main__ - main.py:5475 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,636] INFO - __main__ - main.py:5476 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 15:11:23,637] INFO - __main__ - main.py:5477 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Is first encounter: True
[2025-06-17 15:11:23,637] INFO - __main__ - main.py:5478 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Diagnostic completed: False
[2025-06-17 15:11:23,638] INFO - __main__ - main.py:5484 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,638] INFO - __main__ - main.py:5498 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 15:11:23,638] INFO - __main__ - main.py:5500 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Final phase for AI logic: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,639] INFO - __main__ - main.py:5514 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] NEW SESSION: Using phase-based question index 3 from phase diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,639] INFO - __main__ - main.py:3692 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Diagnostic context validation passed
[2025-06-17 15:11:23,639] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,640] WARNING - __main__ - main.py:5586 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q4_ask_q5' for first encounter
[2025-06-17 15:11:23,640] INFO - __main__ - main.py:3802 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Enhanced diagnostic context with 35 fields
[2025-06-17 15:11:23,640] INFO - __main__ - main.py:5608 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,641] DEBUG - __main__ - main.py:5609 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:11:23,641] WARNING - __main__ - main.py:5617 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖 AI PROMPT GENERATION:
[2025-06-17 15:11:23,641] WARNING - __main__ - main.py:5618 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 15:11:23,642] WARNING - __main__ - main.py:5619 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖   - Student query: Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It ...
[2025-06-17 15:11:23,642] WARNING - __main__ - main.py:5620 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 15:11:23,643] INFO - __main__ - main.py:6493 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] enhance_lesson_content invoked. Query: 'Okay, this is like watching water do amazing trick...'
[2025-06-17 15:11:23,643] INFO - __main__ - main.py:6536 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q4_ask_q5', processed = 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,643] INFO - __main__ - main.py:6547 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q4_ask_q5', module_id: 'materials', gs_subject_slug: 'science'
[2025-06-17 15:11:23,644] INFO - __main__ - main.py:6577 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC ANSWER: Storing Q4 answer from phase diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,644] INFO - __main__ - main.py:6586 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC ANSWER STORED: q4 = 'Okay, this is like watching water do amazing trick...'
[2025-06-17 15:11:23,644] INFO - __main__ - main.py:6587 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Total diagnostic answers now: 4/5
[2025-06-17 15:11:23,644] INFO - __main__ - main.py:6628 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 📝 DIAGNOSTIC PROGRESSION: Continue with 4/5 answers, phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,645] INFO - __main__ - main.py:6766 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,645] INFO - __main__ - main.py:6773 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,650] INFO - __main__ - main.py:6774 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Questions asked: 4/5
[2025-06-17 15:11:23,650] INFO - __main__ - main.py:6775 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Question index: 3
[2025-06-17 15:11:23,650] INFO - __main__ - main.py:6776 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Student answers count: 4
[2025-06-17 15:11:23,651] INFO - __main__ - main.py:6781 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Answer q1: That's a good question!  Right after the water bal...
[2025-06-17 15:11:23,651] INFO - __main__ - main.py:6781 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Answer q2: That's a fun game!  The big rock is a solid!  It's...
[2025-06-17 15:11:23,651] INFO - __main__ - main.py:6781 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Answer q3: That's a clever question!  A large pile of sand wo...
[2025-06-17 15:11:23,652] INFO - __main__ - main.py:6781 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] Answer q4: Okay, this is like watching water do amazing trick...
[2025-06-17 15:11:23,652] INFO - __main__ - main.py:6805 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q4_ask_q5, Answers=4/5
[2025-06-17 15:11:23,652] INFO - __main__ - main.py:6808 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:11:23,653] INFO - __main__ - main.py:6839 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 15:11:23,653] INFO - __main__ - main.py:6853 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,653] INFO - __main__ - main.py:6861 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,653] INFO - __main__ - main.py:6906 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 DIAGNOSTIC EVAL DEBUG: Q5 response analysis:
[2025-06-17 15:11:23,654] INFO - __main__ - main.py:6907 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 DIAGNOSTIC EVAL DEBUG:   - user_query: 'Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!'
[2025-06-17 15:11:23,654] INFO - __main__ - main.py:6908 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 DIAGNOSTIC EVAL DEBUG:   - Final result: True
[2025-06-17 15:11:23,654] INFO - __main__ - main.py:6953 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC PROGRESSION: All questions answered → Evaluation (diagnostic_probing_L5_eval_q5_decide_level)
[2025-06-17 15:11:23,655] WARNING - __main__ - main.py:6964 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧 DIAGNOSTIC INDEX UPDATE COMPLETE: Updated current_q_index_for_prompt to 5 and context to 5
[2025-06-17 15:11:23,656] INFO - __main__ - main.py:7140 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level (interaction 1)
[2025-06-17 15:11:23,656] WARNING - __main__ - main.py:7143 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 15:11:23,657] WARNING - __main__ - main.py:7144 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,657] WARNING - __main__ - main.py:7145 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - User query: 'Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!'
[2025-06-17 15:11:23,658] WARNING - __main__ - main.py:7146 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Is actual student response: True
[2025-06-17 15:11:23,658] WARNING - __main__ - main.py:7147 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Current probing level: 5
[2025-06-17 15:11:23,659] WARNING - __main__ - main.py:7148 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Current question index: 5
[2025-06-17 15:11:23,659] WARNING - __main__ - main.py:7149 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Student answers count: 4
[2025-06-17 15:11:23,659] INFO - __main__ - main.py:7152 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 15:11:23,660] INFO - __main__ - main.py:7153 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,660] INFO - __main__ - main.py:7154 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:23,660] INFO - __main__ - main.py:7155 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧   - user_query: 'Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!'
[2025-06-17 15:11:23,661] INFO - __main__ - main.py:7156 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧   - trusting_ai_state_updates: True
[2025-06-17 15:11:23,661] WARNING - __main__ - main.py:7159 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 15:11:23,661] WARNING - __main__ - main.py:7160 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,661] WARNING - __main__ - main.py:7161 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯   - Calculated new phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:23,663] WARNING - __main__ - main.py:7162 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯   - Phase changed: True
[2025-06-17 15:11:23,666] WARNING - __main__ - main.py:7165 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 15:11:23,667] WARNING - __main__ - main.py:7166 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯   - In diagnostic phase: True
[2025-06-17 15:11:23,667] WARNING - __main__ - main.py:7167 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯   - Student answers stored: {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", 'q4': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!"}
[2025-06-17 15:11:23,668] WARNING - __main__ - main.py:7168 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 15:11:23,668] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Basic Science and Technology', topic='States of Matter', key_concepts='Discover, Three, States, Matter, Transformation', examples=0
[2025-06-17 15:11:23,669] INFO - __main__ - main.py:7229 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_eval_q5_decide_level', q_index='5', total_q_asked='5'
[2025-06-17 15:11:23,669] INFO - __main__ - main.py:7235 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 15:11:23,669] WARNING - __main__ - main.py:7260 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🚀 DIAGNOSTIC PHASE OVERRIDE: AI will receive 'diagnostic_probing_L5_eval_q5_decide_level' instead of 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:23,670] WARNING - __main__ - main.py:7261 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🚀 REASON: Student responded to diagnostic_start_probe, need to progress to Q1
[2025-06-17 15:11:23,670] INFO - __main__ - main.py:7340 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:11:23,670] INFO - __main__ - main.py:7351 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 15:11:23,671] INFO - __main__ - main.py:7357 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Template placeholder successfully substituted
[2025-06-17 15:11:23,671] INFO - __main__ - main.py:7361 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Template contains calculated phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:23,672] INFO - __main__ - main.py:7405 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Prompt truncated from 16535 to 10672 chars for performance
[2025-06-17 15:11:23,672] INFO - __main__ - main.py:7408 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] CONTENT QUALITY DEBUG:
[2025-06-17 15:11:23,672] INFO - __main__ - main.py:7409 -   - Final prompt length: 10672 characters
[2025-06-17 15:11:23,672] INFO - __main__ - main.py:7410 -   - Subject: Basic Science and Technology
[2025-06-17 15:11:23,673] INFO - __main__ - main.py:7411 -   - Topic: States of Matter
[2025-06-17 15:11:23,673] INFO - __main__ - main.py:7412 -   - Key concepts: Discover, Three, States, Matter, Transformation
[2025-06-17 15:11:23,673] INFO - __main__ - main.py:7413 -   - Grade: Primary 5
[2025-06-17 15:11:23,674] INFO - __main__ - main.py:7414 -   - Phase: diagnostic_probing_L5_eval_q4_ask_q5
[2025-06-17 15:11:23,674] INFO - __main__ - main.py:7415 -   - Student: Andrea
[2025-06-17 15:11:23,674] DEBUG - __main__ - main.py:7416 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_eval_q5_decide_level", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_eval_q5_decide_level
• You MUST use...
[2025-06-17 15:11:23,675] INFO - __main__ - main.py:7444 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Gemini API call attempt 1/3
[2025-06-17 15:11:25,014] INFO - __main__ - main.py:7462 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 15:11:25,015] INFO - __main__ - main.py:7481 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Gemini API call completed in 1.34s
[2025-06-17 15:11:25,016] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 459 characters
[2025-06-17 15:11:25,018] INFO - __main__ - main.py:7704 - [353ce001-8aed-46df-85b5-719d6b3e9bdb][enhance_lesson_content] AI response: That's a great observation, Andrea! You've correctly identified water in a glass as a liquid and described its properties. Now let's explore how that liquid water can change.  Imagine you leave that g...
[2025-06-17 15:11:25,019] INFO - __main__ - main.py:7732 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 15:11:25,019] INFO - __main__ - main.py:7733 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 STATE DEBUG: AI response length: 459 chars
[2025-06-17 15:11:25,021] INFO - __main__ - main.py:7745 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_eval_q5_decide_level", "interaction_count": 1}
[2025-06-17 15:11:25,022] INFO - __main__ - main.py:7747 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'interaction_count': 1}
[2025-06-17 15:11:25,022] INFO - __main__ - main.py:7811 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 15:11:25,023] INFO - __main__ - main.py:7817 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 15:11:25,023] INFO - __main__ - main.py:7837 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Auto-corrected: added missing current_probing_level_number
[2025-06-17 15:11:25,024] INFO - __main__ - main.py:7841 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Auto-corrected: added missing current_question_index
[2025-06-17 15:11:25,025] INFO - __main__ - main.py:7867 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 5}
[2025-06-17 15:11:25,025] INFO - __main__ - main.py:7879 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 15:11:25,026] INFO - __main__ - main.py:7922 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 15:11:25,026] INFO - __main__ - main.py:7931 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 5}
[2025-06-17 15:11:25,027] INFO - __main__ - main.py:7989 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=4/5
[2025-06-17 15:11:25,027] INFO - __main__ - main.py:7992 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 📝 DIAGNOSTIC PROGRESSION: Need 1 more answers
[2025-06-17 15:11:25,028] INFO - __main__ - main.py:7994 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 15:11:25,029] INFO - __main__ - main.py:7998 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 15:11:25,029] INFO - __main__ - main.py:8030 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level - trusting AI
[2025-06-17 15:11:25,031] INFO - __main__ - main.py:8075 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,032] WARNING - __main__ - main.py:8113 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 15:11:25,033] WARNING - __main__ - main.py:8114 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Phase: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,034] WARNING - __main__ - main.py:8115 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Question Index: 5
[2025-06-17 15:11:25,034] WARNING - __main__ - main.py:8116 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   - Probing Level: 5
[2025-06-17 15:11:25,035] INFO - __main__ - main.py:8120 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 15:11:25,035] INFO - __main__ - main.py:8121 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_eval_q5_decide_level, Enforced phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,036] INFO - __main__ - main.py:8141 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] [OK] State update block guaranteed by system enforcement
[2025-06-17 15:11:25,036] INFO - __main__ - main.py:8147 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Phase transition: diagnostic_probing_L5_eval_q4_ask_q5 -> diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,037] INFO - __main__ - main.py:3972 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,037] INFO - __main__ - main.py:4074 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,038] INFO - __main__ - main.py:8223 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'current_probing_level_number': 5, 'current_question_index': 5, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", 'q4': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!"}, 'levels_probed_and_failed': []}
[2025-06-17 15:11:25,038] INFO - __main__ - main.py:8259 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 15:11:25,039] INFO - __main__ - main.py:8275 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ AI response already includes personalization
[2025-06-17 15:11:25,039] INFO - __main__ - main.py:8281 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'current_probing_level_number': 5, 'current_question_index': 5, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", 'q4': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!"}, 'levels_probed_and_failed': []}
[2025-06-17 15:11:25,040] INFO - __main__ - main.py:8284 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 15:11:25,040] WARNING - __main__ - main.py:8305 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 15:11:25,041] WARNING - __main__ - main.py:8306 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔄   - AI provided state update: True
[2025-06-17 15:11:25,041] WARNING - __main__ - main.py:8308 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:25,042] WARNING - __main__ - main.py:8309 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:25,042] WARNING - __main__ - main.py:8310 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔄   - Python calculated phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:25,042] INFO - __main__ - main.py:8318 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ⚡ PERFORMANCE METRICS:
[2025-06-17 15:11:25,043] INFO - __main__ - main.py:8319 -   - Total execution time: 1.400s
[2025-06-17 15:11:25,043] WARNING - __main__ - main.py:5643 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 15:11:25,043] WARNING - __main__ - main.py:5644 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖   - Content length: 313 chars
[2025-06-17 15:11:25,044] WARNING - __main__ - main.py:5645 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'current_probing_level_number': 5, 'current_question_index': 5, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", 'q4': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!"}, 'levels_probed_and_failed': []}
[2025-06-17 15:11:25,044] WARNING - __main__ - main.py:5646 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🤖   - Raw state block: None...
[2025-06-17 15:11:25,045] INFO - __main__ - main.py:5662 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 15:11:25,045] INFO - __main__ - main.py:5663 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q5_decide_level, Session=diagnostic_probing_L5_eval_q4_ask_q5, Final=diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,334] INFO - __main__ - main.py:5772 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI processing completed in 1.69s
[2025-06-17 15:11:25,335] WARNING - __main__ - main.py:5783 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q4_ask_q5', new_phase='diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:25,338] INFO - __main__ - main.py:3897 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI state update validation passed: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,339] WARNING - __main__ - main.py:5792 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 15:11:25,340] WARNING - __main__ - main.py:5797 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔄 PHASE TRANSITION: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,341] WARNING - __main__ - main.py:5806 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 15:11:25,342] WARNING - __main__ - main.py:5807 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:25,343] WARNING - __main__ - main.py:5808 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 15:11:25,344] WARNING - __main__ - main.py:5809 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'current_probing_level_number': 5, 'current_question_index': 5, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", 'q4': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!"}, 'levels_probed_and_failed': []}
[2025-06-17 15:11:25,346] WARNING - __main__ - main.py:5810 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:25,348] WARNING - __main__ - main.py:5813 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 💾 FINAL STATE APPLICATION:
[2025-06-17 15:11:25,350] WARNING - __main__ - main.py:5814 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q4_ask_q5'
[2025-06-17 15:11:25,351] WARNING - __main__ - main.py:5815 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q5_decide_level', 'current_probing_level_number': 5, 'current_question_index': 5, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a good question!  Right after the water balloon bursts and the water splashes out, it stays a liquid! It doesn't change into ice or gas just from the balloon breaking.  It just goes from being a liquid inside the balloon to being a liquid splashing on the ground or on you! If it stays on the ground for a long time on a hot day, then it will slowly turn into a gas and disappear.", 'q2': 'That\'s a fun game!  The big rock is a solid!  It\'s a good hiding place because:  It has its own fixed shape. It doesn\'t squish or spread out like water, so it stays big and covers you. It\'s hard and strong, so you can\'t see through it! It blocks your view from the person who is "it." It stays in one place, it doesn\'t flow away like water or float away like air!', 'q3': "That's a clever question!  A large pile of sand would probably not be as good a hiding place as the rock.  Here's why:  Even though each tiny grain of sand is a solid (it has its own shape), when you put lots of them together in a pile, the pile itself can change shape easily.  If you lean on it, the sand might shift or slide down, revealing you! It's not one big, solid block like the rock. Someone could easily push some sand aside or see through little gaps if the pile isn't packed perfectly. The rock is one big, strong solid that keeps its shape, so it's a much more reliable hiding spot! The sand pile is a collection of small solids that can move around.", 'q4': "Okay, this is like watching water do amazing tricks!  The Water in the Glass: This is a liquid!  It flows, splashes, and takes the shape of the glass. The tiny water parts (molecules) are dancing around and sliding past each other. The Ice Cube: This is a solid!  It's hard, keeps its own shape, and doesn't flow. The tiny water parts are holding hands and are stuck very close together. How it changes: If the ice cube gets warm (like in your drink), it melts and turns back into a liquid! The tiny parts start dancing again. The Steam from the Pot: This is a gas!  You can barely see it because the tiny water parts are flying around super fast and spreading out everywhere, filling the whole air! How it changes: If the water in the glass gets really hot (like in the pot), it evaporates (or boils!) and turns into gas (steam)! The tiny parts get so much energy they fly far apart. And if the steam gets cold (like if it touches a cool lid), it condenses and turns back into tiny drops of liquid water! The tiny parts slow down and get closer again. So, it's all just water, but depending on how hot or cold it is, its tiny parts act differently and make it look like a solid, a liquid, or a gas!"}, 'levels_probed_and_failed': []}
[2025-06-17 15:11:25,353] WARNING - __main__ - main.py:5816 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-06-17 15:11:25,354] WARNING - __main__ - main.py:5817 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 💾   - Phase change: True
[2025-06-17 15:11:25,355] INFO - __main__ - main.py:3929 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 15:11:25,355] INFO - __main__ - main.py:3930 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Phase transition: diagnostic_probing_L5_eval_q4_ask_q5 -> diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,356] INFO - __main__ - main.py:3931 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Current level: 5
[2025-06-17 15:11:25,357] INFO - __main__ - main.py:3932 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Question index: 5
[2025-06-17 15:11:25,358] INFO - __main__ - main.py:3933 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   First encounter: True
[2025-06-17 15:11:25,359] INFO - __main__ - main.py:3938 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Answers collected: 4
[2025-06-17 15:11:25,359] INFO - __main__ - main.py:3939 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Levels failed: 0
[2025-06-17 15:11:25,360] INFO - __main__ - main.py:3897 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI state update validation passed: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,361] INFO - __main__ - main.py:3943 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   State update valid: True
[2025-06-17 15:11:25,362] INFO - __main__ - main.py:3950 - [353ce001-8aed-46df-85b5-719d6b3e9bdb]   Diagnostic complete: False
[2025-06-17 15:11:25,364] WARNING - __main__ - main.py:5829 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 5, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 15:11:25,925] WARNING - __main__ - main.py:5856 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 15:11:25,926] WARNING - __main__ - main.py:5857 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:25,929] WARNING - __main__ - main.py:5858 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Probing Level: 5
[2025-06-17 15:11:25,936] WARNING - __main__ - main.py:5859 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Question Index: 5
[2025-06-17 15:11:25,936] WARNING - __main__ - main.py:5860 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Diagnostic Complete: False
[2025-06-17 15:11:26,826] INFO - __main__ - main.py:5919 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ Updated existing session document: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:11:26,827] WARNING - __main__ - main.py:5920 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 15:11:26,828] WARNING - __main__ - main.py:5921 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Session ID: session_d7acbbee-2d8b-4e6a-ac1c-4808edf5db80
[2025-06-17 15:11:26,829] WARNING - __main__ - main.py:5922 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Phase transition: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level
[2025-06-17 15:11:26,831] WARNING - __main__ - main.py:5923 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] ✅   - Interaction logged successfully
[2025-06-17 15:11:26,832] INFO - __main__ - main.py:9216 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 15:11:26,833] DEBUG - __main__ - main.py:2753 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 15:11:26,835] DEBUG - __main__ - main.py:5969 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] No final assessment data found in AI response
[2025-06-17 15:11:26,837] INFO - __main__ - main.py:6034 - [353ce001-8aed-46df-85b5-719d6b3e9bdb] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 15:11:26,838] WARNING - __main__ - main.py:625 - High response time detected: 6.96s for enhance_content_api
[2025-06-17 15:11:26,839] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 15:11:26] "POST /api/enhance-content HTTP/1.1" 200 -
